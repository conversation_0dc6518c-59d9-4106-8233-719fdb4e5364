import React from 'react';
import styled from 'styled-components';
import { Fa<PERSON>lus, FaMoon, FaSun } from 'react-icons/fa';

const HeaderContainer = styled.header`
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 0;
  border-bottom: 1px solid ${props => props.theme.border};
`;

const LogoContainer = styled.div`
  display: flex;
  align-items: center;
  gap: 12px;
`;

const LogoImage = styled.img`
  width: 50px;
  height: 50px;
  object-fit: contain;
`;

const LogoTextContainer = styled.div`
  display: flex;
  flex-direction: column;
`;

const LogoTitle = styled.div`
  font-size: 32px;
  font-weight: 700;
  color: ${props => props.theme.primary};
  cursor: default;
  line-height: 1.2;
`;

const LogoSubtitle = styled.div`
  font-size: 14px;
  color: ${props => props.theme.textSecondary};
  font-weight: 400;
`;

const ButtonsGroup = styled.div`
  display: flex;
  gap: 10px;
`;

const Button = styled.button`
  display: flex;
  align-items: center;
  gap: 6px;
  background-color: ${props => props.primary ? props.theme.primary : props.theme.borderLight};
  color: ${props => props.primary ? 'white' : props.theme.textSecondary};
  border: none;
  border-radius: 4px;
  padding: 8px 12px;
  cursor: pointer;
  font-size: 14px;
  transition: all 0.2s;

  &:hover {
    background-color: ${props => props.primary ? props.theme.primaryDark : props.theme.border};
    color: ${props => props.primary ? 'white' : props.theme.text};
  }
`;

const ThemeToggle = styled.button`
  display: flex;
  align-items: center;
  justify-content: center;
  width: 36px;
  height: 36px;
  border-radius: 50%;
  background-color: ${props => props.theme.borderLight};
  color: ${props => props.darkMode ? props.theme.warning : props.theme.primary};
  border: none;
  cursor: pointer;
  transition: all 0.2s;
  
  &:hover {
    background-color: ${props => props.theme.border};
    transform: rotate(15deg);
  }
`;

const Header = ({ onNewProject, onToggleTheme, darkMode }) => {
  return (
    <HeaderContainer>
      <LogoContainer>
        <LogoImage src="/logo/eagle1.png" alt="Logo" />
        <LogoTextContainer>
          <LogoTitle>
            CodePrompt
          </LogoTitle>
          <LogoSubtitle>
            Where Architecture Meets Intention
          </LogoSubtitle>
        </LogoTextContainer>
      </LogoContainer>
      <ButtonsGroup>
        <ThemeToggle 
          onClick={onToggleTheme}
          title={darkMode ? "切换到亮色模式" : "切换到暗色模式"}
          darkMode={darkMode}
        >
          {darkMode ? <FaSun /> : <FaMoon />}
        </ThemeToggle>
        <Button onClick={onNewProject} primary>
          <FaPlus />
          新建项目
        </Button>
      </ButtonsGroup>
    </HeaderContainer>
  );
};

export default Header; 