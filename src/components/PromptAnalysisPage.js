/**
 * 提示词分析页面 - 展示存储的评估数据用于分析
 */

import React, { useState, useEffect } from 'react';
import { localStorageService } from '../services/localStorageService';
import { promptEvaluationService } from '../services/promptEvaluationService';
import ManualEvaluationForm from './ManualEvaluationForm';
import './PromptAnalysisPage.css';

const PromptAnalysisPage = () => {
  // 评估方式状态
  const [evaluationMethod, setEvaluationMethod] = useState('llm'); // 'llm' 或 'manual'

  // 数据状态
  const [llmEvaluations, setLlmEvaluations] = useState([]);
  const [manualEvaluations, setManualEvaluations] = useState([]);
  const [prompts, setPrompts] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [dbStatus, setDbStatus] = useState('checking'); // 'checking', 'ready', 'fallback', 'error'
  const [esStatus, setEsStatus] = useState('checking'); // 'checking', 'connected', 'disconnected'

  // UI状态
  const [selectedPrompt, setSelectedPrompt] = useState(null);
  const [showEvaluationForm, setShowEvaluationForm] = useState(false);
  const [evaluatingPromptType, setEvaluatingPromptType] = useState(null); // 'original' 或 'optimized'

  useEffect(() => {
    loadData();
    checkElasticsearchStatus();
  }, []);

  // 检查 Elasticsearch 连接状态
  const checkElasticsearchStatus = async () => {
    try {
      setEsStatus('checking');
      console.log('🔍 检查 Elasticsearch 连接状态...');

      // 等待 promptEvaluationService 初始化完成
      await promptEvaluationService.initializeStorageBackend();

      if (promptEvaluationService.useElasticsearch) {
        setEsStatus('connected');
        console.log('✅ Elasticsearch 连接成功');
      } else {
        setEsStatus('disconnected');
        console.log('⚠️ Elasticsearch 连接失败，使用本地存储');
      }
    } catch (error) {
      console.error('❌ 检查 Elasticsearch 状态失败:', error);
      setEsStatus('disconnected');
    }
  };

  const loadData = async () => {
    try {
      setLoading(true);
      setError(null);

      console.log('🔍 开始加载提示词分析数据...');

      // 确保存储后端已初始化
      await promptEvaluationService.initializeStorageBackend();

      let promptsData = { prompts: [] };
      let llmEvals = [];
      let manualEvals = [];

      // 优先尝试从Elasticsearch加载数据
      if (promptEvaluationService.useElasticsearch) {
        try {
          console.log('📊 从Elasticsearch加载数据...');

          const [esPrompts, esEvaluations] = await Promise.all([
            promptEvaluationService.getAllPrompts(),
            promptEvaluationService.getAllEvaluations()
          ]);

          // 转换Elasticsearch数据格式以适配现有UI
          promptsData.prompts = esPrompts.map(prompt => ({
            id: prompt.id,
            prompt_content: prompt.content,
            prompt_style: prompt.type === 'original' ? 'original' : 'enhanced',
            score_judged_directly: prompt.score / 100, // 转换为0-1范围
            score_judged_by_downstream_task: prompt.score / 100,
            tags: [],
            metadata: {
              evaluationId: prompt.evaluationId,
              type: prompt.type
            },
            created_at: prompt.timestamp,
            updated_at: prompt.timestamp,
            version: 1,
            // 添加UI需要的字段
            originalPrompt: prompt.content,
            optimizedPrompt: prompt.optimizedContent || null,
            timestamp: prompt.timestamp
          }));

          // 转换评估数据格式
          llmEvals = esEvaluations.map(evaluation => ({
            id: evaluation.id,
            workflowId: evaluation.workflowId,
            timestamp: evaluation.timestamp,
            // 确保originalPrompt和enhancedPrompt是字符串，而不是对象
            originalPrompt: typeof evaluation.originalPrompt === 'string'
              ? evaluation.originalPrompt
              : evaluation.originalPrompt?.content || '',
            enhancedPrompt: typeof evaluation.enhancedPrompt === 'string'
              ? evaluation.enhancedPrompt
              : evaluation.enhancedPrompt?.content || '',
            improvement: evaluation.improvement,
            metadata: evaluation.metadata,
            // 保留原始的评估对象以供详细显示使用
            originalEvaluation: typeof evaluation.originalPrompt === 'object'
              ? evaluation.originalPrompt?.evaluation
              : null,
            enhancedEvaluation: typeof evaluation.enhancedPrompt === 'object'
              ? evaluation.enhancedPrompt?.evaluation
              : null
          }));

          console.log('✅ 从Elasticsearch加载数据成功');
          setDbStatus('ready');

        } catch (esError) {
          console.warn('⚠️ 从Elasticsearch加载数据失败，降级到本地存储:', esError.message);
          // 降级到本地存储
          promptEvaluationService.useElasticsearch = false;
        }
      }

      // 如果Elasticsearch不可用，从本地存储加载数据
      if (!promptEvaluationService.useElasticsearch) {
        console.log('📊 从本地存储加载数据...');

        // 确保数据库已准备就绪
        const dbReady = await localStorageService.ensureDBReady();
        setDbStatus(dbReady ? 'ready' : 'fallback');
        console.log('✅ 数据库准备就绪，状态:', dbReady ? 'IndexedDB' : 'localStorage');

        // 加载所有数据
        const [localPromptsData, localLlmEvals, localManualEvals] = await Promise.all([
          localStorageService.searchPrompts(''),
          localStorageService.getLLMEvaluations(),
          localStorageService.getManualEvaluations()
        ]);

        promptsData = localPromptsData;
        llmEvals = localLlmEvals || [];
        manualEvals = localManualEvals || [];
      }

      // 人工评估始终从本地存储加载（因为目前只存储在本地）
      try {
        const localManualEvals = await localStorageService.getManualEvaluations();
        manualEvals = localManualEvals || [];
      } catch (manualError) {
        console.warn('⚠️ 加载人工评估数据失败:', manualError.message);
        manualEvals = [];
      }

      console.log('📊 数据加载结果:', {
        prompts: promptsData.prompts?.length || 0,
        llmEvaluations: llmEvals?.length || 0,
        manualEvaluations: manualEvals?.length || 0,
        dataSource: promptEvaluationService.useElasticsearch ? 'Elasticsearch' : 'LocalStorage'
      });

      setPrompts(promptsData.prompts || []);
      setLlmEvaluations(llmEvals || []);
      setManualEvaluations(manualEvals || []);

    } catch (err) {
      console.error('❌ 加载数据失败:', err);
      setError(`加载数据失败: ${err.message}。请尝试刷新页面或检查浏览器控制台获取更多信息。`);
    } finally {
      setLoading(false);
    }
  };

  // 处理人工评估提交
  const handleManualEvaluationSubmit = async (evaluationData) => {
    try {
      // 确保评估数据包含正确的workflowId
      const enhancedEvaluationData = {
        ...evaluationData,
        workflowId: selectedPrompt?.id || evaluationData.workflowId
      };

      await localStorageService.saveManualEvaluation(enhancedEvaluationData);
      setShowEvaluationForm(false);
      setSelectedPrompt(null);
      setEvaluatingPromptType(null);
      await loadData(); // 重新加载数据
    } catch (err) {
      setError('保存人工评估失败: ' + err.message);
    }
  };

  // 开始人工评估
  const startManualEvaluation = (prompt, promptType) => {
    setSelectedPrompt(prompt);
    setEvaluatingPromptType(promptType);
    setShowEvaluationForm(true);
  };

  // 清空所有数据
  const handleClearAllData = async () => {
    const confirmMessage = `确定要清空所有评估数据吗？

这将删除：
• 所有提示词数据 (${prompts.length} 条)
• 所有LLM评估记录 (${llmEvaluations.length} 条)
• 所有人工评估记录 (${manualEvaluations.length} 条)

此操作不可撤销！`;

    if (window.confirm(confirmMessage)) {
      try {
        setLoading(true);
        setError(null);

        console.log('🗑️ 开始清空所有评估数据...');

        // 清空本地存储数据
        await localStorageService.clearAllData();

        // 如果使用 Elasticsearch，也尝试清空（虽然当前没有实现，但为将来扩展做准备）
        if (esStatus === 'connected') {
          console.log('⚠️ Elasticsearch 数据清空功能暂未实现，仅清空本地数据');
        }

        console.log('✅ 所有数据已清空');

        // 重新加载数据以更新UI
        await loadData();

        alert('所有评估数据已成功清空！');

      } catch (err) {
        console.error('❌ 清空数据失败:', err);
        setError(`清空数据失败: ${err.message}`);
      } finally {
        setLoading(false);
      }
    }
  };



  if (loading) {
    return (
      <div className="analysis-page">
        <div className="loading">加载评估数据中...</div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="analysis-page">
        <div className="error-container">
          <h2>⚠️ 数据加载失败</h2>
          <div className="error-message">{error}</div>
          <div className="error-actions">
            <button className="btn-primary" onClick={loadData}>🔄 重试</button>
            <button
              className="btn-secondary"
              onClick={() => {
                console.log('重置IndexedDB数据库...');
                if (window.confirm('这将清除所有本地数据，确定要重置数据库吗？')) {
                  const deleteRequest = indexedDB.deleteDatabase('PromptEvaluationDB');
                  deleteRequest.onsuccess = () => {
                    alert('数据库已重置，请刷新页面');
                    window.location.reload();
                  };
                }
              }}
            >
              🗑️ 重置数据库
            </button>
          </div>
          <div className="error-help">
            <h3>故障排除建议：</h3>
            <ul>
              <li>检查浏览器是否支持IndexedDB</li>
              <li>清除浏览器缓存和数据</li>
              <li>尝试在隐私模式下打开页面</li>
              <li>查看浏览器控制台获取详细错误信息</li>
            </ul>
          </div>
        </div>
      </div>
    );
  }

  // 如果显示人工评估表单
  if (showEvaluationForm && selectedPrompt) {
    // 安全地获取提示词内容
    const getPromptContent = (prompt) => {
      if (typeof prompt === 'string') return prompt;
      if (prompt?.content) return prompt.content;
      return '';
    };

    const promptContent = evaluatingPromptType === 'original'
      ? getPromptContent(selectedPrompt.originalPrompt)
      : getPromptContent(selectedPrompt.optimizedPrompt);

    return (
      <div className="analysis-page">
        <ManualEvaluationForm
          prompt={promptContent}
          promptType={evaluatingPromptType}
          workflowId={selectedPrompt?.id}
          onSubmit={handleManualEvaluationSubmit}
          onCancel={() => {
            setShowEvaluationForm(false);
            setSelectedPrompt(null);
            setEvaluatingPromptType(null);
          }}
        />
      </div>
    );
  }

  return (
    <div className="analysis-page">
      {/* 顶部标题栏 */}
      <div className="analysis-header">
        <h1>提示词评估分析</h1>

        {/* 评估方式选择器 */}
        <div className="evaluation-method-selector">
          <button
            className={`evaluation-method-tab ${evaluationMethod === 'llm' ? 'active' : ''}`}
            onClick={() => setEvaluationMethod('llm')}
          >
            🤖 LLM-as-a-Judge 评估
          </button>
          <button
            className={`evaluation-method-tab ${evaluationMethod === 'manual' ? 'active' : ''}`}
            onClick={() => setEvaluationMethod('manual')}
          >
            👤 人工评估
          </button>
        </div>
      </div>

      {/* 左右布局主体 */}
      <div className="analysis-main">
        {/* 左侧统计面板 */}
        <div className="analysis-sidebar">
          <div className="stats-panel">
            <h3>统计概览</h3>
            <div className="stats-list">
              <div className="stat-item">
                <span className="stat-label">提示词总数</span>
                <span className="stat-value">{prompts.length}</span>
              </div>
              <div className="stat-item">
                <span className="stat-label">LLM评估数</span>
                <span className="stat-value">{llmEvaluations.length}</span>
              </div>
              <div className="stat-item">
                <span className="stat-label">人工评估数</span>
                <span className="stat-value">{manualEvaluations.length}</span>
              </div>
              <div className="stat-item">
                <span className="stat-label">存储状态</span>
                <span className={`stat-value ${
                  esStatus === 'connected' ? 'status-ready' :
                  esStatus === 'checking' ? 'status-checking' :
                  dbStatus === 'ready' ? 'status-fallback' : 'status-error'
                }`}>
                  {esStatus === 'connected' ? '🟢 Elasticsearch' :
                   esStatus === 'checking' ? '🔄 检查中...' :
                   dbStatus === 'ready' ? '🟡 IndexedDB' : '🔴 localStorage'}
                </span>
                {esStatus === 'disconnected' && (
                  <button
                    className="btn-small btn-secondary"
                    onClick={checkElasticsearchStatus}
                    style={{ marginLeft: '8px', fontSize: '12px', padding: '4px 8px' }}
                  >
                    🔄 重新检查ES
                  </button>
                )}
              </div>
            </div>

            {/* 数据管理操作 */}
            <div className="data-management-panel">
              <h4>数据管理</h4>
              <div className="management-actions">
                <button
                  className="btn-danger btn-small"
                  onClick={handleClearAllData}
                  disabled={loading || (prompts.length === 0 && llmEvaluations.length === 0 && manualEvaluations.length === 0)}
                  title={
                    prompts.length === 0 && llmEvaluations.length === 0 && manualEvaluations.length === 0
                      ? "暂无数据可清空"
                      : "清空所有评估数据"
                  }
                >
                  🗑️ 清空所有数据
                </button>
                <button
                  className="btn-secondary btn-small"
                  onClick={loadData}
                  disabled={loading}
                  title="重新加载数据"
                >
                  🔄 刷新数据
                </button>
              </div>
            </div>
          </div>
        </div>

        {/* 右侧内容区域 */}
        <div className="analysis-content">
        {evaluationMethod === 'llm' ? (
          // LLM评估视图
          <div className="llm-evaluations-section">
            <div className="section-header">
              <h2>LLM-as-a-Judge 评估记录</h2>
              <p>基于8个维度的自动化评估结果</p>
            </div>

            {llmEvaluations.length === 0 ? (
              <div className="empty-state">
                <p>暂无LLM评估数据</p>
                <p>请在优化页面使用LLM评估功能</p>
              </div>
            ) : (
              <div className="evaluations-grid">
                {llmEvaluations.map(evaluation => {
                  // 安全地获取原始提示词内容
                  const originalPromptContent = typeof evaluation.originalPrompt === 'string'
                    ? evaluation.originalPrompt
                    : evaluation.originalPrompt?.content || '';

                  // 获取LLM评估的八个维度分数 - 修正数据结构访问路径
                  const originalLlmEval = evaluation.originalPrompt?.evaluation?.llmEvaluation?.evaluation?.scores ||
                                         evaluation.originalEvaluation?.llmEvaluation?.evaluation?.scores ||
                                         evaluation.originalEvaluation?.scores;
                  const enhancedLlmEval = evaluation.enhancedPrompt?.evaluation?.llmEvaluation?.evaluation?.scores ||
                                        evaluation.enhancedEvaluation?.llmEvaluation?.evaluation?.scores ||
                                        evaluation.enhancedEvaluation?.scores;



                  // 获取人工评估总分（如果存在）
                  const originalManualScore = evaluation.originalEvaluation?.humanEvaluation?.totalScore;
                  const enhancedManualScore = evaluation.enhancedEvaluation?.humanEvaluation?.totalScore;

                  // 查找对应的人工评估记录
                  const relatedManualEvals = manualEvaluations.filter(manual =>
                    manual.workflowId === evaluation.workflowId ||
                    manual.prompt === originalPromptContent
                  );

                  return (
                    <div key={evaluation.id} className="evaluation-card">
                      <div className="evaluation-header">
                        <h3>{originalPromptContent.substring(0, 50)}...</h3>
                        <span className="evaluation-score">
                          {evaluation.improvement?.absoluteImprovement?.toFixed(1) || 0}% 改进
                        </span>
                      </div>
                      <div className="evaluation-meta">
                        <span>评估时间: {new Date(evaluation.timestamp).toLocaleString()}</span>
                      </div>

                      {/* LLM评估八个维度 */}
                      {originalLlmEval && (
                        <div className="llm-dimensions-section">
                          <h4 className="section-title">LLM评估 (8个维度)</h4>
                          <div className="dimensions-comparison">
                            <div className="dimensions-column">
                              <h5>原始提示词</h5>
                              <div className="dimensions-grid">
                                {Object.entries(originalLlmEval).map(([key, value]) => {
                                  const score = typeof value === 'object' ? value.score : value;
                                  const dimensionNames = {
                                    specificity: '具体性',
                                    clarity: '清晰度',
                                    structure: '结构性',
                                    completeness: '完整性',
                                    roleDefinition: '角色定义',
                                    outputFormat: '输出格式',
                                    constraints: '约束条件',
                                    actionability: '可操作性'
                                  };
                                  return (
                                    <div key={key} className="dimension-item">
                                      <span className="dimension-name">{dimensionNames[key] || key}</span>
                                      <span className="dimension-score">{typeof score === 'number' ? score.toFixed(1) : score}</span>
                                    </div>
                                  );
                                })}
                              </div>
                            </div>

                            {enhancedLlmEval && (
                              <div className="dimensions-column">
                                <h5>优化提示词</h5>
                                <div className="dimensions-grid">
                                  {Object.entries(enhancedLlmEval).map(([key, value]) => {
                                    const score = typeof value === 'object' ? value.score : value;
                                    const dimensionNames = {
                                      specificity: '具体性',
                                      clarity: '清晰度',
                                      structure: '结构性',
                                      completeness: '完整性',
                                      roleDefinition: '角色定义',
                                      outputFormat: '输出格式',
                                      constraints: '约束条件',
                                      actionability: '可操作性'
                                    };
                                    return (
                                      <div key={key} className="dimension-item">
                                        <span className="dimension-name">{dimensionNames[key] || key}</span>
                                        <span className="dimension-score">{typeof score === 'number' ? score.toFixed(1) : score}</span>
                                      </div>
                                    );
                                  })}
                                </div>
                              </div>
                            )}
                          </div>
                        </div>
                      )}

                      {/* 人工评估总分 */}
                      <div className="manual-scores-section">
                        <div className="manual-scores-header">
                          <h4 className="section-title">人工评估总分</h4>
                        </div>
                        <div className="manual-scores-grid">
                          {/* 原始提示词评估 */}
                          <div className="manual-score-item">
                            <div className="score-info">
                              <span className="score-label">原始提示词</span>
                              {(originalManualScore || relatedManualEvals.find(e => e.promptType === 'original')) ? (
                                <span className="score-value">
                                  {originalManualScore?.toFixed(1) ||
                                   relatedManualEvals.find(e => e.promptType === 'original')?.totalScore?.toFixed(1) ||
                                   'N/A'}/10
                                </span>
                              ) : (
                                <span className="score-value no-score">未评估</span>
                              )}
                            </div>
                            {!(originalManualScore || relatedManualEvals.find(e => e.promptType === 'original')) && (
                              <button
                                className="btn-add-evaluation"
                                onClick={() => {
                                  // 构造一个临时的prompt对象用于评估
                                  const promptForEval = {
                                    id: evaluation.workflowId || evaluation.id,
                                    originalPrompt: originalPromptContent,
                                    timestamp: evaluation.timestamp
                                  };
                                  startManualEvaluation(promptForEval, 'original');
                                }}
                                title="为原始提示词添加人工评估"
                              >
                                + 添加评估
                              </button>
                            )}
                          </div>

                          {/* 优化提示词评估 */}
                          {evaluation.enhancedPrompt && (
                            <div className="manual-score-item">
                              <div className="score-info">
                                <span className="score-label">优化提示词</span>
                                {(enhancedManualScore || relatedManualEvals.find(e => e.promptType === 'optimized')) ? (
                                  <span className="score-value">
                                    {enhancedManualScore?.toFixed(1) ||
                                     relatedManualEvals.find(e => e.promptType === 'optimized')?.totalScore?.toFixed(1) ||
                                     'N/A'}/10
                                  </span>
                                ) : (
                                  <span className="score-value no-score">未评估</span>
                                )}
                              </div>
                              {!(enhancedManualScore || relatedManualEvals.find(e => e.promptType === 'optimized')) && (
                                <button
                                  className="btn-add-evaluation"
                                  onClick={() => {
                                    // 构造一个临时的prompt对象用于评估
                                    const promptForEval = {
                                      id: evaluation.workflowId || evaluation.id,
                                      originalPrompt: originalPromptContent,
                                      optimizedPrompt: evaluation.enhancedPrompt.content || evaluation.enhancedPrompt,
                                      timestamp: evaluation.timestamp
                                    };
                                    startManualEvaluation(promptForEval, 'optimized');
                                  }}
                                  title="为优化提示词添加人工评估"
                                >
                                  + 添加评估
                                </button>
                              )}
                            </div>
                          )}
                        </div>
                      </div>
                    </div>
                  );
                })}
              </div>
            )}
          </div>
        ) : (
          // 人工评估视图
          <div className="manual-evaluations-section">
            <div className="section-header">
              <h2>人工评估记录</h2>
              <p>基于7个一级维度的人工评估结果</p>
            </div>

            {/* 提示词列表，用于选择进行人工评估 */}
            <div className="prompts-for-evaluation">
              <h3>可评估的提示词</h3>
              {prompts.length === 0 ? (
                <div className="empty-state">
                  <p>暂无提示词数据</p>
                  <p>请先创建一些提示词</p>
                </div>
              ) : (
                <div className="prompts-grid">
                  {prompts.map(prompt => {
                    const originalManualEval = manualEvaluations.find(e => e.workflowId === prompt.id && e.promptType === 'original');
                    const optimizedManualEval = manualEvaluations.find(e => e.workflowId === prompt.id && e.promptType === 'optimized');

                    // 安全地获取原始提示词内容
                    const originalPromptContent = typeof prompt.originalPrompt === 'string'
                      ? prompt.originalPrompt
                      : prompt.originalPrompt?.content || prompt.prompt_content || '';

                    return (
                      <div key={prompt.id} className="prompt-card">
                        <div className="prompt-header">
                          <h4>{originalPromptContent.substring(0, 60)}...</h4>
                          <span className="prompt-date">
                            {new Date(prompt.timestamp).toLocaleDateString()}
                          </span>
                        </div>

                        <div className="evaluation-actions">
                          <div className="evaluation-status">
                            <div className="status-item">
                              <span>原始提示词:</span>
                              {originalManualEval ? (
                                <span className="evaluated">已评估 ({originalManualEval.totalScore.toFixed(1)}/10)</span>
                              ) : (
                                <button
                                  className="btn-evaluate"
                                  onClick={() => startManualEvaluation(prompt, 'original')}
                                >
                                  开始评估
                                </button>
                              )}
                            </div>

                            {prompt.optimizedPrompt && (
                              <div className="status-item">
                                <span>优化提示词:</span>
                                {optimizedManualEval ? (
                                  <span className="evaluated">已评估 ({optimizedManualEval.totalScore.toFixed(1)}/10)</span>
                                ) : (
                                  <button
                                    className="btn-evaluate"
                                    onClick={() => startManualEvaluation(prompt, 'optimized')}
                                  >
                                    开始评估
                                  </button>
                                )}
                              </div>
                            )}
                          </div>
                        </div>
                      </div>
                    );
                  })}
                </div>
              )}
            </div>

            {/* 已完成的人工评估列表 */}
            {manualEvaluations.length > 0 && (
              <div className="completed-evaluations">
                <h3>已完成的人工评估</h3>
                <div className="evaluations-grid">
                  {manualEvaluations.map(evaluation => (
                    <div key={evaluation.id} className="evaluation-card">
                      <div className="evaluation-header">
                        <h4>{evaluation.promptType === 'original' ? '原始' : '优化'} 提示词评估</h4>
                        <span className="evaluation-score">
                          {evaluation.totalScore.toFixed(1)}/10
                        </span>
                      </div>
                      <div className="evaluation-meta">
                        <span>评估时间: {new Date(evaluation.timestamp).toLocaleString()}</span>
                      </div>
                      <div className="categories-preview">
                        {Object.entries(evaluation.categoryScores).slice(0, 4).map(([category, score]) => (
                          <div key={category} className="category-item">
                            <span className="category-name">{category}</span>
                            <span className="category-score">{score.toFixed(1)}</span>
                          </div>
                        ))}
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            )}
          </div>
        )}
        </div>
      </div>
    </div>
  );
};

export default PromptAnalysisPage;
