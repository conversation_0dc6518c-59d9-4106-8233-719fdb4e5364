import React, { useState, useEffect } from 'react';
import styled from 'styled-components';
import { autoOptimizePromptStream, autoOptimizePromptStreamVersionB, guidedOptimizePromptStream } from '../services/promptOptimizerService';
import { promptEvaluationService } from '../services/promptEvaluationService';
import { promptManager } from '../prompts/index.js';
import { countTokens } from '../utils/tokenCounter.js';

const Container = styled.div`
  display: flex;
  flex-direction: column;
  flex: 1;
  overflow: hidden;
  position: relative;
  width: 100%;
`;

const ContentContainer = styled.div`
  display: flex;
  flex-direction: column;
  gap: 20px;
  flex: 1;
  height: calc(100vh - 120px);
  overflow: hidden;
  padding: 0 24px;
  width: 100%;
`;

const OriginalPromptSection = styled.div`
  flex: 0 0 auto;
  height: 350px;
  min-height: 350px;
  max-height: 350px;
  display: flex;
  flex-direction: column;

  @media (max-width: 768px) {
    height: 300px;
    min-height: 300px;
    max-height: 300px;
  }

  @media (max-width: 480px) {
    height: 250px;
    min-height: 250px;
    max-height: 250px;
  }
`;

const OptimizedVersionsContainer = styled.div`
  display: flex;
  gap: 20px;
  flex: 1;
  overflow: hidden;

  @media (max-width: 1024px) {
    flex-direction: column;
    height: auto;
  }
`;

const Column = styled.div`
  display: flex;
  flex-direction: column;
  flex: 1;
  overflow: hidden;
  background-color: ${props => props.theme.surface};
  transition: all 0.2s ease-in-out;
  position: relative;
  height: 100%;
  border-radius: 4px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);

  @media (max-width: 1024px) {
    max-width: 100%;
    height: 400px;
  }
`;

const OriginalColumn = styled(Column)`
  height: 100%;
  max-width: 100%;
  flex: 1;
  display: flex;
  flex-direction: column;
`;

const OptimizedColumn = styled(Column)`
  max-width: calc(50% - 10px);
  height: 500px;           /* Fixed height for optimized versions */
  min-height: 500px;       /* Minimum height constraint */
  max-height: 500px;       /* Maximum height constraint */

  @media (max-width: 1024px) {
    max-width: 100%;
    height: 400px;         /* Fixed height for tablets */
    min-height: 400px;
    max-height: 400px;
  }

  @media (max-width: 768px) {
    height: 350px;         /* Fixed height for smaller tablets */
    min-height: 350px;
    max-height: 350px;
  }

  @media (max-width: 480px) {
    height: 300px;         /* Fixed height for mobile */
    min-height: 300px;
    max-height: 300px;
  }
`;





const ColumnHeader = styled.div`
  background-color: ${props => props.theme.surface};
  border-bottom: 1px solid ${props => props.theme.border};
  padding: 0.75rem 1.25rem;
  height: 3.5rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
  box-sizing: border-box;
  border-top-left-radius: 4px;
  border-top-right-radius: 4px;
`;

const OriginalHeader = styled(ColumnHeader)`
  background-color: ${props => props.theme.surface};
`;

const EnhancedHeader = styled(ColumnHeader)`
  background: linear-gradient(to right, ${props => props.theme.surface}, ${props => props.theme.primary}10);
  position: relative;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-right: 1rem;
`;

const ColumnTitle = styled.h3`
  margin: 0;
  font-size: 1.1rem;
  color: ${props => props.theme.text};
  display: flex;
  align-items: center;
  gap: 0.75rem;
`;

const CopyButton = styled.button`
  background: none;
  border: none;
  color: ${props => props.theme.primary};
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem;
  border-radius: 4px;
  font-size: 0.9rem;
  transition: all 0.15s ease;
  
  &:hover {
    background-color: ${props => props.theme.primaryLight};
  }
  
  &:disabled {
    opacity: 0.5;
    cursor: not-allowed;
  }
`;

const ActionButtonsGroup = styled.div`
  display: flex;
  gap: 0.75rem;
  z-index: 10;
  margin-left: auto;
  padding-left: 1rem;
  height: 2.25rem;
`;

const ProgressIndicator = styled.span`
  display: inline-block;
  padding: 0.25rem 0.75rem;
  background-color: ${props => props.theme.warning};
  color: white;
  border-radius: 2rem;
  font-size: 0.75rem;
  font-weight: 600;
  margin-left: 0.75rem;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  animation: pulse 1.5s infinite;

  @keyframes pulse {
    0% {
      opacity: 0.8;
    }
    50% {
      opacity: 1;
    }
    100% {
      opacity: 0.8;
    }
  }
`;

const EditorContainer = styled.div`
  display: flex;
  width: 100%;
  flex: 1;
  overflow: hidden;
  position: relative;
  background-color: ${props => props.theme.background};
  border-bottom-left-radius: 4px;
  border-bottom-right-radius: 4px;
`;

const OriginalEditorContainer = styled(EditorContainer)`
  background-color: ${props => props.theme.background};
  flex: 1;
  min-height: 0; /* Allow flex child to shrink below content size */
  height: calc(100% - 3.5rem); /* Subtract header height */
`;

const EnhancedEditorContainer = styled(EditorContainer)`
  background-color: ${props => props.theme.primary}05;
  background-image: linear-gradient(to bottom right, ${props => props.theme.background}, ${props => props.theme.primary}10);
  flex: 1;
  min-height: 0; /* Allow flex child to shrink below content size */
  height: calc(100% - 3.5rem); /* Subtract header height */
  overflow: hidden; /* Ensure container doesn't expand */
`;

const OptimizedContentWrapper = styled.div`
  display: flex;
  flex-direction: column;
  height: 100%;
  overflow-y: auto; /* Enable vertical scrolling for the entire content */
  overflow-x: hidden; /* Disable horizontal scrolling */

  /* Custom scrollbar styling */
  &::-webkit-scrollbar {
    width: 8px;
  }

  &::-webkit-scrollbar-track {
    background: ${props => props.theme.background};
    border-radius: 4px;
  }

  &::-webkit-scrollbar-thumb {
    background: ${props => props.theme.border};
    border-radius: 4px;

    &:hover {
      background: ${props => props.theme.textMuted};
    }
  }
`;

const LineNumbers = styled.div`
  width: 3rem;
  flex-shrink: 0;
  padding: 1.25rem 0.5rem;
  background-color: ${props => props.theme.background};
  border-right: 1px solid ${props => props.theme.border};
  font-family: 'Monaco', 'Menlo', monospace;
  font-size: 0.85rem;
  line-height: 1.5;
  color: ${props => props.theme.textMuted};
  text-align: right;
  user-select: none;
`;

const OriginalLineNumbers = styled(LineNumbers)`
  background-color: ${props => props.theme.background};
  color: ${props => props.theme.textMuted};
  overflow-y: hidden; /* Hide scrollbar on line numbers */
  height: 100%; /* Match the text area height */
`;

const EnhancedLineNumbers = styled(LineNumbers)`
  background-color: ${props => props.theme.primary}10;
  color: ${props => props.theme.primary}90;
  border-right: 1px solid ${props => props.theme.primary}30;
`;

const TextArea = styled.textarea`
  width: 100%;
  flex: 1;
  padding: 1.25rem;
  border: none;
  background-color: ${props => props.theme.background};
  color: ${props => props.theme.text};
  font-family: 'Monaco', 'Menlo', monospace;
  font-size: 0.95rem;
  line-height: 1.5;
  resize: none;
  outline: none;
  transition: background-color 0.2s ease;
  /* Default behavior - can be overridden by styled components that extend this */
  white-space: pre;
  overflow-wrap: normal;
  overflow-x: auto;
`;

const OriginalTextArea = styled(TextArea)`
  background-color: ${props => props.theme.background};
  color: ${props => props.theme.text};
  placeholder: "输入您的原始Prompt...";
  white-space: pre-wrap; /* Enable text wrapping while preserving line breaks */
  overflow-wrap: break-word; /* Break long words if necessary */
  overflow-x: hidden; /* Disable horizontal scrolling */
  overflow-y: auto; /* Enable vertical scrolling */
  word-break: break-word; /* Additional word breaking for very long words */
  height: 100%; /* Fill the container height */
  min-height: 200px; /* Minimum height for usability */
  scroll-behavior: smooth; /* Smooth scrolling */

  /* Custom scrollbar styling for better UX */
  &::-webkit-scrollbar {
    width: 8px;
  }

  &::-webkit-scrollbar-track {
    background: ${props => props.theme.background};
    border-radius: 4px;
  }

  &::-webkit-scrollbar-thumb {
    background: ${props => props.theme.border};
    border-radius: 4px;

    &:hover {
      background: ${props => props.theme.textMuted};
    }
  }

  /* Focus state improvements */
  &:focus {
    box-shadow: inset 0 0 0 1px ${props => props.theme.primary}40;
  }

  /* Mobile responsiveness */
  @media (max-width: 768px) {
    font-size: 0.9rem;
    padding: 1rem;
  }

  @media (max-width: 480px) {
    font-size: 0.85rem;
    padding: 0.75rem;
    line-height: 1.4;
  }
`;

const EnhancedContent = styled.pre`
  width: 100%;
  flex: 1;
  margin: 0;
  padding: 1.25rem;
  background-color: transparent;
  color: ${props => props.theme.text};
  font-family: 'Monaco', 'Menlo', monospace;
  font-size: 0.95rem;
  line-height: 1.5;
  white-space: pre-wrap; /* Enable text wrapping while preserving line breaks */
  overflow-wrap: break-word; /* Break long words if necessary */
  word-break: break-word; /* Additional word breaking for very long words */
  overflow-x: hidden; /* Disable horizontal scrolling */
  overflow-y: visible; /* Let the wrapper handle scrolling */
  transition: background-color 0.2s ease;
  position: relative;

  /* Custom scrollbar styling for consistency */
  &::-webkit-scrollbar {
    width: 8px;
  }

  &::-webkit-scrollbar-track {
    background: ${props => props.theme.background};
    border-radius: 4px;
  }

  &::-webkit-scrollbar-thumb {
    background: ${props => props.theme.border};
    border-radius: 4px;

    &:hover {
      background: ${props => props.theme.textMuted};
    }
  }

  &.streaming::after {
    content: '|';
    display: inline-block;
    opacity: 1;
    animation: blink 0.7s infinite;
    position: relative;
    margin-left: 2px;
  }
  
  &.complete {
    background-color: ${props => props.theme.primary}05;
  }

  @keyframes blink {
    0%, 100% { opacity: 1; }
    50% { opacity: 0; }
  }
`;

const ActionButton = styled.button`
  padding: 0.55rem 1.25rem;
  border: none;
  border-radius: 6px;
  background-color: ${props => props.theme.primary};
  color: white;
  cursor: pointer;
  font-weight: 500;
  font-size: 0.875rem;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  transition: all 0.2s ease;
  box-shadow: 0 1px 2px rgba(16, 24, 40, 0.05);
  position: relative;
  overflow: hidden;
  z-index: 1;
  height: 100%;
  
  &:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 5px rgba(16, 24, 40, 0.1);
  }

  &:active {
    transform: translateY(0);
  }

  &:disabled {
    opacity: 0.65;
    cursor: not-allowed;
    pointer-events: none;
  }
`;

const AutoOptimizeButton = styled(ActionButton)`
  background: linear-gradient(to right, #2a6bc0, #2e7fe0);
  font-weight: 500;

  &:hover {
    background: linear-gradient(to right, #2359a8, #2673cc);
  }
`;

const StorageStatusContainer = styled.div`
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem;
  background-color: ${props => props.theme.background};
  border: 1px solid ${props => props.theme.border};
  border-radius: 6px;
  font-size: 0.75rem;
`;

const StorageStatusText = styled.span`
  color: ${props => props.theme.textSecondary};
  font-weight: 500;
  white-space: nowrap;
`;

const StorageCheckButton = styled.button`
  padding: 0.25rem 0.5rem;
  border: none;
  border-radius: 4px;
  background-color: ${props => props.theme.primaryLight};
  color: ${props => props.theme.primary};
  cursor: pointer;
  font-size: 0.75rem;
  font-weight: 500;
  display: flex;
  align-items: center;
  gap: 0.25rem;
  transition: all 0.2s ease;

  &:hover {
    background-color: ${props => props.theme.primary};
    color: white;
  }

  &:disabled {
    opacity: 0.65;
    cursor: not-allowed;
    pointer-events: none;
  }
`;

// GuidedOptimizeButton 暂时注释掉，因为引导优化功能已暂时关闭
// const GuidedOptimizeButton = styled(ActionButton)`
//   background: white;
//   border: 1px solid #d0d5dd;
//   color: #344054;
//   box-shadow: 0 1px 2px rgba(16, 24, 40, 0.05);
//   font-weight: 500;
//
//   &:hover {
//     background: #f9fafb;
//     color: #1d2939;
//     border-color: #d0d5dd;
//     box-shadow: 0 1px 3px rgba(16, 24, 40, 0.1);
//   }
//
//   &:disabled {
//     border-color: ${props => props.theme.border};
//     color: ${props => props.theme.textMuted};
//     background-color: white;
//   }
// `;

const LoadingSpinner = styled.div`
  display: inline-block;
  width: 1.1rem;
  height: 1.1rem;
  border: 2px solid ${props => props.theme.primaryLight};
  border-top-color: ${props => props.theme.primary};
  border-radius: 50%;
  animation: spin 1s linear infinite;

  @keyframes spin {
    to {
      transform: rotate(360deg);
    }
  }
`;

const Modal = styled.div`
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.6);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: 1rem;
  backdrop-filter: blur(4px);
  opacity: 0;
  animation: fadeIn 0.2s forwards;
  
  @keyframes fadeIn {
    to {
      opacity: 1;
    }
  }
`;

const ModalContent = styled.div`
  background-color: ${props => props.theme.surface};
  border-radius: 12px;
  width: 550px;
  max-width: 90%;
  padding: 2rem;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
  transform: translateY(20px);
  animation: slideUp 0.3s forwards;
  
  @keyframes slideUp {
    to {
      transform: translateY(0);
    }
  }
`;

const ModalTitle = styled.h3`
  margin: 0 0 1.25rem 0;
  color: ${props => props.theme.text};
  font-size: 1.25rem;
  font-weight: 600;
`;

const TextInput = styled.input`
  width: 100%;
  padding: 0.9rem 1.25rem;
  border: 1px solid ${props => props.theme.border};
  border-radius: 6px;
  background-color: ${props => props.theme.background};
  color: ${props => props.theme.text};
  margin-bottom: 1.75rem;
  font-size: 1rem;
  transition: all 0.2s ease;
  overflow-x: hidden; /* Prevent horizontal overflow */
  text-overflow: ellipsis; /* Show ellipsis for very long text */

  &:focus {
    outline: none;
    border-color: ${props => props.theme.primary};
    box-shadow: 0 0 0 3px ${props => props.theme.primaryLight}40;
  }
`;

const ModalActions = styled.div`
  display: flex;
  justify-content: flex-end;
  gap: 1rem;
`;

const ModalButton = styled(ActionButton)`
  padding: 0.6rem 1.2rem;
  font-size: 0.85rem;
`;

const ModalCancelButton = styled(ModalButton)`
  background: white;
  border: 1px solid ${props => props.theme.borderLight};
  color: #333;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.06);
  font-weight: 500;
  
  &:hover {
    background: #f9f9f9;
    color: #222;
    border-color: ${props => props.theme.border};
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    
    &::before {
      background: linear-gradient(
        90deg,
        transparent 0%,
        rgba(0, 0, 0, 0.05) 50%,
        transparent 100%
      );
    }
  }
  
  &:disabled {
    border-color: ${props => props.theme.border};
    color: ${props => props.theme.textMuted};
    background-color: white;
  }
`;

const ModalConfirmButton = styled(ModalButton)`
  background: linear-gradient(135deg, #3182ce, #2c5282);
  font-weight: 500;
  
  &:hover {
    background: linear-gradient(135deg, #2b6cb0, #1e3a8a);
  }
`;

const ImprovementList = styled.div`
  margin-top: 1rem;
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
  max-height: 300px;
  overflow-y: auto;
  padding-right: 0.5rem;

  /* Custom scrollbar styling */
  &::-webkit-scrollbar {
    width: 6px;
  }

  &::-webkit-scrollbar-track {
    background: ${props => props.theme.background};
    border-radius: 3px;
  }

  &::-webkit-scrollbar-thumb {
    background: ${props => props.theme.border};
    border-radius: 3px;

    &:hover {
      background: ${props => props.theme.textMuted};
    }
  }
`;

const ImprovementItem = styled.div`
  padding: 0.875rem 1rem;
  background-color: ${props => props.theme.surface};
  border-radius: 6px;
  border-left: 3px solid ${props => props.theme.primary};
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
  transition: all 0.2s ease;
  font-size: 0.9rem;
  line-height: 1.5;
  word-wrap: break-word;
  overflow-wrap: break-word;

  &:hover {
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
    transform: translateX(2px);
  }

  strong {
    color: ${props => props.theme.primary};
    font-weight: 600;
    display: block;
    margin-bottom: 0.25rem;
  }
`;

const SectionTitle = styled.h4`
  margin: 0 0 1rem 0;
  color: ${props => props.theme.text};
  font-size: 1.1rem;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  
  &::before {
    content: "";
    display: block;
    width: 4px;
    height: 1rem;
    background-color: ${props => props.theme.primary};
    border-radius: 2px;
  }
`;

const ContentSection = styled.div`
  padding: 1rem 1.25rem 1.25rem;
  border-top: 1px solid ${props => props.theme.border}20;
  margin-top: 1rem;
  background-color: ${props => props.theme.background}50;
  border-radius: 0 0 4px 4px;
  flex-shrink: 0;
`;

const ErrorMessage = styled.div`
  color: ${props => props.theme.error};
  padding: 1rem;
  border-left: 3px solid ${props => props.theme.error};
  background-color: ${props => props.theme.error}15;
  border-radius: 4px;
  margin: 0.5rem 0;
  font-size: 0.95rem;
`;

const IconWrapper = styled.span`
  display: inline-flex;
  align-items: center;
  justify-content: center;
  margin-right: 0.25rem;
  font-size: 1rem;
`;

const TokenCounter = styled.div`
  position: absolute;
  bottom: 0.5rem;
  right: 1rem;
  background-color: ${props => props.theme.primary}20;
  color: ${props => props.theme.primary};
  padding: 0.25rem 0.75rem;
  border-radius: 1rem;
  font-size: 0.75rem;
  font-weight: 500;
  z-index: 10;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  display: flex;
  align-items: center;
  gap: 0.5rem;
`;

const EnhancedTokenCounter = styled(TokenCounter)`
  background-color: ${props => props.theme.primary}30;
  color: ${props => props.theme.primary};
`;

const EvaluationModal = styled(Modal)`
  z-index: 1100;
`;

const EvaluationModalContent = styled(ModalContent)`
  width: 800px;
  max-width: 95%;
  max-height: 90vh;
  overflow-y: auto;
`;

const EvaluationHeader = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
`;

const EvaluationTitle = styled.h2`
  margin: 0;
  color: ${props => props.theme.text};
  font-size: 1.5rem;
  font-weight: 600;
`;

const ComparisonContainer = styled.div`
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1.5rem;
  margin-bottom: 2rem;
`;

const PromptCard = styled.div`
  border: 1px solid ${props => props.theme.border};
  border-radius: 8px;
  padding: 1.25rem;
  background-color: ${props => props.isWinner ? props.theme.primary + '10' : props.theme.background};
  border-color: ${props => props.isWinner ? props.theme.primary : props.theme.border};
  position: relative;
`;

const CardHeader = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
`;

const CardTitle = styled.h3`
  margin: 0;
  color: ${props => props.theme.text};
  font-size: 1.1rem;
  font-weight: 600;
`;

const WinnerBadge = styled.div`
  background-color: ${props => props.theme.primary};
  color: white;
  padding: 0.25rem 0.75rem;
  border-radius: 1rem;
  font-size: 0.75rem;
  font-weight: 600;
`;

const ScoreGrid = styled.div`
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 0.5rem;
  margin-bottom: 1rem;
`;

const ScoreItem = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.5rem;
  background-color: ${props => props.theme.surface};
  border-radius: 4px;
  font-size: 0.85rem;
`;

const ScoreValue = styled.span`
  font-weight: 600;
  color: ${props => {
    if (props.score >= 8) return props.theme.success || '#10b981';
    if (props.score >= 6) return props.theme.warning || '#f59e0b';
    return props.theme.error || '#ef4444';
  }};
`;

const TotalScore = styled.div`
  text-align: center;
  padding: 1rem;
  background-color: ${props => props.theme.primary}15;
  border-radius: 6px;
  margin-bottom: 1rem;
`;

const TotalScoreValue = styled.div`
  font-size: 2rem;
  font-weight: 700;
  color: ${props => props.theme.primary};
  margin-bottom: 0.25rem;
`;

const TotalScoreLabel = styled.div`
  font-size: 0.9rem;
  color: ${props => props.theme.textMuted};
`;

const AnalysisSection = styled.div`
  margin-bottom: 1.5rem;
`;

const AnalysisTitle = styled.h4`
  margin: 0 0 0.75rem 0;
  color: ${props => props.theme.text};
  font-size: 1rem;
  font-weight: 600;
`;

const AnalysisList = styled.ul`
  margin: 0;
  padding-left: 1.25rem;
  color: ${props => props.theme.text};
  font-size: 0.9rem;
  line-height: 1.5;
`;

const RecommendationSection = styled.div`
  background-color: ${props => props.theme.primary}10;
  border-left: 4px solid ${props => props.theme.primary};
  padding: 1.25rem;
  border-radius: 6px;
  margin-bottom: 1.5rem;
`;

const RecommendationTitle = styled.h4`
  margin: 0 0 0.75rem 0;
  color: ${props => props.theme.primary};
  font-size: 1.1rem;
  font-weight: 600;
`;

const RecommendationText = styled.p`
  margin: 0;
  color: ${props => props.theme.text};
  font-size: 0.95rem;
  line-height: 1.6;
`;

const EvaluationActions = styled.div`
  display: flex;
  justify-content: space-between;
  gap: 1rem;
  margin-top: 2rem;
`;

const UsePromptButton = styled(ActionButton)`
  flex: 1;
  background: linear-gradient(135deg, ${props => props.theme.primary}, ${props => props.theme.primary}dd);

  &:hover {
    background: linear-gradient(135deg, ${props => props.theme.primary}dd, ${props => props.theme.primary}bb);
  }
`;

const CloseEvaluationButton = styled(ModalCancelButton)`
  flex: 0 0 auto;
  min-width: 100px;
`;

const PromptOptimizer = () => {
  const [originalPrompt, setOriginalPrompt] = useState('');

  // Version A states
  const [streamedTextA, setStreamedTextA] = useState('');
  const [improvementsA, setImprovementsA] = useState([]);
  const [isOptimizingA, setIsOptimizingA] = useState(false);
  const [optimizationCompleteA, setOptimizationCompleteA] = useState(false);
  const [errorA, setErrorA] = useState(null);
  const [enhancedLineNumbersA, setEnhancedLineNumbersA] = useState([1]);
  const [streamProgressA, setStreamProgressA] = useState(0);

  // Version B states
  const [streamedTextB, setStreamedTextB] = useState('');
  const [improvementsB, setImprovementsB] = useState([]);
  const [isOptimizingB, setIsOptimizingB] = useState(false);
  const [optimizationCompleteB, setOptimizationCompleteB] = useState(false);
  const [errorB, setErrorB] = useState(null);
  const [enhancedLineNumbersB, setEnhancedLineNumbersB] = useState([1]);
  const [streamProgressB, setStreamProgressB] = useState(0);

  // Common states
  const [lineNumbers, setLineNumbers] = useState([1]);
  
  // 添加token计数状态
  const [originalTokenCount, setOriginalTokenCount] = useState(0);
  const [isCountingTokens, setIsCountingTokens] = useState(false);

  // 引导式优化相关状态
  const [showGuidanceModal, setShowGuidanceModal] = useState(false);
  const [guidanceText, setGuidanceText] = useState('');

  // 评估相关状态
  const [showEvaluationModal, setShowEvaluationModal] = useState(false);
  const [evaluationResult, setEvaluationResult] = useState(null);
  const [isEvaluating, setIsEvaluating] = useState(false);
  const [isEvaluatingB, setIsEvaluatingB] = useState(false);
  const [evaluationError, setEvaluationError] = useState(null);

  // 个别评估状态
  const [isEvaluatingOriginal, setIsEvaluatingOriginal] = useState(false);

  // 存储后端状态
  const [storageBackendStatus, setStorageBackendStatus] = useState(null);
  const [isCheckingBackend, setIsCheckingBackend] = useState(false);

  useEffect(() => {
    // 计算原始文本的行号
    const lines = originalPrompt.split('\n');
    const numbers = Array.from({ length: Math.max(lines.length, 1) }, (_, i) => i + 1);
    setLineNumbers(numbers);
  }, [originalPrompt]);

  // 初始化存储后端状态
  useEffect(() => {
    const initializeStorageStatus = async () => {
      try {
        const status = promptEvaluationService.getStorageBackendStatus();
        setStorageBackendStatus(status);
      } catch (error) {
        console.error('获取存储后端状态失败:', error);
      }
    };

    initializeStorageStatus();
  }, []);

  // 检查并切换存储后端
  const handleCheckStorageBackend = async () => {
    setIsCheckingBackend(true);
    try {
      const isESAvailable = await promptEvaluationService.checkAndSwitchStorageBackend();
      const status = promptEvaluationService.getStorageBackendStatus();
      setStorageBackendStatus(status);

      if (isESAvailable) {
        alert('✅ ElasticSearch连接成功！已切换到ES存储');
      } else {
        alert('⚠️ ElasticSearch连接失败，继续使用本地存储');
      }
    } catch (error) {
      console.error('检查存储后端失败:', error);
      alert('❌ 检查存储后端时发生错误');
    } finally {
      setIsCheckingBackend(false);
    }
  };

  // 切换模拟连接结果（用于测试）
  const handleToggleSimulation = () => {
    try {
      const newResult = promptEvaluationService.toggleSimulationResult();
      alert(`🔄 模拟连接结果已切换为${newResult ? '成功' : '失败'}`);

      // 更新状态显示
      const status = promptEvaluationService.getStorageBackendStatus();
      setStorageBackendStatus(status);
    } catch (error) {
      console.error('切换模拟结果失败:', error);
      alert('❌ 切换模拟结果时发生错误');
    }
  };

  useEffect(() => {
    // 计算增强文本A的行号
    if (streamedTextA) {
      const lines = streamedTextA.split('\n');
      const numbers = Array.from({ length: Math.max(lines.length, 1) }, (_, i) => i + 1);
      setEnhancedLineNumbersA(numbers);
    }
  }, [streamedTextA]);

  useEffect(() => {
    // 计算增强文本B的行号
    if (streamedTextB) {
      const lines = streamedTextB.split('\n');
      const numbers = Array.from({ length: Math.max(lines.length, 1) }, (_, i) => i + 1);
      setEnhancedLineNumbersB(numbers);
    }
  }, [streamedTextB]);

  // 添加token计数效果
  useEffect(() => {
    const calculateTokens = async () => {
      if (originalPrompt) {
        setIsCountingTokens(true);
        try {
          const count = await countTokens(originalPrompt);
          setOriginalTokenCount(count);
        } catch (error) {
          console.error('计算原始prompt的token数失败:', error);
        } finally {
          setIsCountingTokens(false);
        }
      } else {
        setOriginalTokenCount(0);
      }
    };

    // 使用防抖，避免频繁计算
    const debounceTimer = setTimeout(calculateTokens, 300);
    return () => clearTimeout(debounceTimer);
  }, [originalPrompt]);

  // 计算优化后prompt A的token数
  const [enhancedTokenCountA, setEnhancedTokenCountA] = useState(0);
  const [enhancedTokenCountB, setEnhancedTokenCountB] = useState(0);

  useEffect(() => {
    const calculateEnhancedTokensA = async () => {
      if (streamedTextA) {
        try {
          const count = await countTokens(streamedTextA);
          setEnhancedTokenCountA(count);
        } catch (error) {
          console.error('计算优化后prompt A的token数失败:', error);
        }
      } else {
        setEnhancedTokenCountA(0);
      }
    };

    calculateEnhancedTokensA();
  }, [streamedTextA]);

  useEffect(() => {
    const calculateEnhancedTokensB = async () => {
      if (streamedTextB) {
        try {
          const count = await countTokens(streamedTextB);
          setEnhancedTokenCountB(count);
        } catch (error) {
          console.error('计算优化后prompt B的token数失败:', error);
        }
      } else {
        setEnhancedTokenCountB(0);
      }
    };

    calculateEnhancedTokensB();
  }, [streamedTextB]);

  // 处理流式响应的回调函数 - Version A
  const handleStreamChunkA = (text, progress) => {
    // 从流式内容中提取有效的优化提示词内容
    if (progress && progress.json && progress.json.optimizedPrompt) {
      // 如果已经解析出了JSON，直接显示优化后的提示词
      let optimizedContent = progress.json.optimizedPrompt;
      
      // 处理从服务器返回的转义字符
      optimizedContent = optimizedContent
        .replace(/\\n/g, '\n')
        .replace(/\\"/g, '"')
        .replace(/\\\\/g, '\\');
      
      setStreamedTextA(optimizedContent);

      // 如果还返回了改进点，也保存起来
      if (progress.json.improvementPoints || progress.json.guidedImprovements) {
        setImprovementsA(progress.json.improvementPoints || progress.json.guidedImprovements || []);
      }
    } else {
      // 尝试从原始文本中提取JSON和优化后的提示词
      try {
        // 查找JSON部分并尝试解析
        const jsonMatch = text.match(/{[\s\S]*}/);
        if (jsonMatch) {
          try {
            const parsedJson = JSON.parse(jsonMatch[0]);
            if (parsedJson && parsedJson.optimizedPrompt) {
              // 处理转义字符
              let cleanContent = parsedJson.optimizedPrompt
                .replace(/\\n/g, '\n')
                .replace(/\\"/g, '"')
                .replace(/\\\\/g, '\\');
              
              setStreamedTextA(cleanContent);

              // 如果还有优化点信息，也保存
              if (parsedJson.improvementPoints || parsedJson.guidedImprovements) {
                setImprovementsA(parsedJson.improvementPoints || parsedJson.guidedImprovements || []);
              }
              return;
            }
          } catch (e) {
            // JSON解析可能失败，继续其他提取方法
            console.log('完整JSON解析失败', e);
          }
        }
        
        // JSON解析失败，尝试用正则提取内容
        const promptRegex = /"optimizedPrompt"\s*:\s*"([\s\S]*?)(?:"\s*,|"\s*}|$)/;
        const match = text.match(promptRegex);
        if (match && match[1]) {
          // 处理转义字符
          let promptContent = match[1]
            .replace(/\\n/g, '\n')
            .replace(/\\"/g, '"')
            .replace(/\\\\/g, '\\');
          
          // 移除可能的JSON结束标记
          promptContent = promptContent.replace(/",\s*"improvementPoints.*$/s, '');
          promptContent = promptContent.replace(/",\s*"guidedImprovements.*$/s, '');
          
          setStreamedTextA(promptContent);
          return;
        }
        
        // 如果找不到完整匹配，但已经开始显示了部分内容，可以尝试提取部分
        if (text.includes('"optimizedPrompt"')) {
          const startIdx = text.indexOf('"optimizedPrompt"') + 18; // 跳过"optimizedPrompt": 
          let endIdx = text.length;
          
          // 尝试找到内容的结束位置（遇到下一个字段）
          const nextField = text.indexOf('",', startIdx);
          if (nextField > 0) {
            endIdx = nextField;
          }
          
          // 提取并处理内容
          let extractedText = text.substring(startIdx, endIdx)
            .replace(/^"/, '') // 移除开头的引号
            .replace(/\\n/g, '\n')
            .replace(/\\"/g, '"')
            .replace(/\\\\/g, '\\');
          
          // 移除末尾多余字符
          extractedText = extractedText.replace(/",\s*".*$/s, '');
          
          setStreamedTextA(extractedText);
        }
      } catch (err) {
        // 解析失败时的处理（保留原有行为）
        console.log('提取优化提示词出错', err);
      }
    }
    
    if (typeof progress === 'object') {
      if (progress.progress) {
        setStreamProgressA(progress.progress);
      }

      // 更新处理状态
      if (progress.status === 'complete') {
        setOptimizationCompleteA(true);
      }
    }
  };

  // 处理流式响应的回调函数 - Version B
  const handleStreamChunkB = (text, progress) => {
    // Version B 使用简化的处理方式
    if (progress && progress.json && progress.json.optimizedPrompt) {
      // 如果已经解析出了JSON，直接显示优化后的提示词
      let optimizedContent = progress.json.optimizedPrompt;

      // 处理从服务器返回的转义字符
      optimizedContent = optimizedContent
        .replace(/\\n/g, '\n')
        .replace(/\\"/g, '"')
        .replace(/\\\\/g, '\\');

      setStreamedTextB(optimizedContent);

      // 如果还返回了改进点，也保存起来
      if (progress.json.improvementPoints || progress.json.guidedImprovements) {
        setImprovementsB(progress.json.improvementPoints || progress.json.guidedImprovements || []);
      }
    } else {
      // 对于Version B，直接显示原始文本（因为它使用不同的格式）
      setStreamedTextB(text);
    }

    if (typeof progress === 'object') {
      if (progress.progress) {
        setStreamProgressB(progress.progress);
      }

      // 更新处理状态
      if (progress.status === 'complete') {
        setOptimizationCompleteB(true);
      }
    }
  };

  const handleAutoOptimizeA = async () => {
    if (!originalPrompt.trim()) return;

    setIsOptimizingA(true);
    setErrorA(null);
    setStreamedTextA('');
    setStreamProgressA(0);
    setImprovementsA([]);
    setOptimizationCompleteA(false);

    try {
      // 使用流式API处理优化 Version A
      const result = await autoOptimizePromptStream(originalPrompt, handleStreamChunkA);
      setImprovementsA(result.improvementPoints || []);
      setOptimizationCompleteA(true);
    } catch (err) {
      console.error('自动优化A失败:', err);
      setErrorA(err.message);
    } finally {
      setIsOptimizingA(false);
    }
  };

  const handleAutoOptimizeB = async () => {
    if (!originalPrompt.trim()) return;

    setIsOptimizingB(true);
    setErrorB(null);
    setStreamedTextB('');
    setStreamProgressB(0);
    setImprovementsB([]);
    setOptimizationCompleteB(false);

    try {
      // 使用流式API处理优化 Version B
      const result = await autoOptimizePromptStreamVersionB(originalPrompt, handleStreamChunkB);
      setImprovementsB(result.improvementPoints || []);
      setOptimizationCompleteB(true);
    } catch (err) {
      console.error('自动优化B失败:', err);
      setErrorB(err.message);
    } finally {
      setIsOptimizingB(false);
    }
  };

  const handleGuidedOptimize = async () => {
    if (!originalPrompt.trim() || !guidanceText.trim()) return;

    setShowGuidanceModal(false);
    setIsOptimizingB(true);
    setErrorB(null);
    setStreamedTextB('');
    setStreamProgressB(0);
    setImprovementsB([]);
    setOptimizationCompleteB(false);

    try {
      // 使用流式API处理引导式优化 (在Version B中显示)
      const result = await guidedOptimizePromptStream(originalPrompt, guidanceText, handleStreamChunkB);
      setImprovementsB(result.guidedImprovements || []);
      setOptimizationCompleteB(true);
    } catch (err) {
      console.error('引导式优化失败:', err);
      setErrorB(err.message);
    } finally {
      setIsOptimizingB(false);
    }
  };



  const handleEvaluatePrompts = async () => {
    if (!originalPrompt.trim()) {
      alert('请先输入原始Prompt');
      return;
    }

    if (!displayedPromptA.trim()) {
      alert('请先生成优化版本A的Prompt');
      return;
    }

    // 使用Version A作为优化版本进行对比评估
    const enhancedPrompt = displayedPromptA;

    setIsEvaluating(true);
    setEvaluationError(null);

    try {
      // 使用新的评估工作流程
      const result = await promptEvaluationService.evaluatePromptWorkflow(
        originalPrompt,
        enhancedPrompt,
        null, // 暂时不提供人工评估分数
        { temperature: 0.1 }
      );

      // 转换结果格式以适配现有的评估模态框
      const adaptedResult = {
        originalEvaluation: {
          scores: result.originalEvaluation.llmEvaluation.evaluation.scores,
          totalScore: result.originalEvaluation.llmEvaluation.evaluation.totalScore,
          averageScore: result.originalEvaluation.llmEvaluation.evaluation.averageScore,
          strengths: result.originalEvaluation.llmEvaluation.evaluation.strengths,
          weaknesses: result.originalEvaluation.llmEvaluation.evaluation.weaknesses || []
        },
        optimizedEvaluation: {
          scores: result.enhancedEvaluation.llmEvaluation.evaluation.scores,
          totalScore: result.enhancedEvaluation.llmEvaluation.evaluation.totalScore,
          averageScore: result.enhancedEvaluation.llmEvaluation.evaluation.averageScore,
          strengths: result.enhancedEvaluation.llmEvaluation.evaluation.strengths,
          weaknesses: result.enhancedEvaluation.llmEvaluation.evaluation.weaknesses || []
        },
        comparison: {
          winner: result.enhancedEvaluation.finalScore > result.originalEvaluation.finalScore ? 'optimized' : 'original',
          improvementAreas: result.enhancedEvaluation.llmEvaluation.evaluation.improvementSuggestions || [],
          recommendation: result.report.recommendations.join('; '),
          keyDifferences: [`改进度: ${result.improvement.summary}`]
        },
        // 添加优化点信息
        optimizationImprovements: {
          versionA: improvementsA || [],
          versionUsed: 'A'
        }
      };

      setEvaluationResult(adaptedResult);
      setShowEvaluationModal(true);

      console.log('评估完成，数据库ID:', result.databaseId);
    } catch (err) {
      console.error('Prompt评估失败:', err);
      setEvaluationError(err.message);
    } finally {
      setIsEvaluating(false);
    }
  };

  const handleEvaluatePromptsB = async () => {
    if (!originalPrompt.trim()) {
      alert('请先输入原始Prompt');
      return;
    }

    if (!displayedPromptB.trim()) {
      alert('请先生成优化版本B的Prompt');
      return;
    }

    // 使用Version B作为优化版本进行对比评估
    const enhancedPrompt = displayedPromptB;

    setIsEvaluatingB(true);
    setEvaluationError(null);

    try {
      // 使用新的评估工作流程
      const result = await promptEvaluationService.evaluatePromptWorkflow(
        originalPrompt,
        enhancedPrompt,
        null, // 暂时不提供人工评估分数
        { temperature: 0.1 }
      );

      // 转换结果格式以适配现有的评估模态框
      const adaptedResult = {
        originalEvaluation: {
          scores: result.originalEvaluation.llmEvaluation.evaluation.scores,
          totalScore: result.originalEvaluation.llmEvaluation.evaluation.totalScore,
          averageScore: result.originalEvaluation.llmEvaluation.evaluation.averageScore,
          strengths: result.originalEvaluation.llmEvaluation.evaluation.strengths,
          weaknesses: result.originalEvaluation.llmEvaluation.evaluation.weaknesses || []
        },
        optimizedEvaluation: {
          scores: result.enhancedEvaluation.llmEvaluation.evaluation.scores,
          totalScore: result.enhancedEvaluation.llmEvaluation.evaluation.totalScore,
          averageScore: result.enhancedEvaluation.llmEvaluation.evaluation.averageScore,
          strengths: result.enhancedEvaluation.llmEvaluation.evaluation.strengths,
          weaknesses: result.enhancedEvaluation.llmEvaluation.evaluation.weaknesses || []
        },
        comparison: {
          winner: result.enhancedEvaluation.finalScore > result.originalEvaluation.finalScore ? 'optimized' : 'original',
          improvementAreas: result.enhancedEvaluation.llmEvaluation.evaluation.improvementSuggestions || [],
          recommendation: result.report.recommendations.join('; '),
          keyDifferences: [`改进度: ${result.improvement.summary}`]
        },
        // 添加优化点信息
        optimizationImprovements: {
          versionB: improvementsB || [],
          versionUsed: 'B'
        }
      };

      setEvaluationResult(adaptedResult);
      setShowEvaluationModal(true);

      console.log('评估完成，数据库ID:', result.databaseId);
    } catch (err) {
      console.error('Prompt评估失败:', err);
      setEvaluationError(err.message);
    } finally {
      setIsEvaluatingB(false);
    }
  };

  // 个别评估函数
  const handleEvaluateIndividual = async (prompt, promptType, setLoadingState) => {
    if (!prompt.trim()) {
      alert(`请先输入${promptType}内容`);
      return;
    }

    setLoadingState(true);
    setEvaluationError(null);

    try {
      const result = await promptManager.evaluatePromptComprehensive(
        prompt,
        promptType,
        null, // 暂时不提供人工评估分数
        { temperature: 0.1 }
      );

      const adaptedResult = {
        singleEvaluation: {
          promptType,
          prompt,
          scores: result.llmEvaluation.evaluation.scores,
          totalScore: result.llmEvaluation.evaluation.totalScore,
          averageScore: result.llmEvaluation.evaluation.averageScore,
          strengths: result.llmEvaluation.evaluation.strengths,
          weaknesses: result.llmEvaluation.evaluation.weaknesses || [],
          recommendations: result.llmEvaluation.evaluation.improvementSuggestions || []
        },
        isSingleEvaluation: true
      };

      setEvaluationResult(adaptedResult);
      setShowEvaluationModal(true);

      console.log(`${promptType}评估完成`);
    } catch (err) {
      console.error(`${promptType}评估失败:`, err);
      setEvaluationError(err.message);
    } finally {
      setLoadingState(false);
    }
  };

  const handleEvaluateOriginal = () => {
    handleEvaluateIndividual(originalPrompt, '原始Prompt', setIsEvaluatingOriginal);
  };



  const handleCopyToClipboard = async (text) => {
    try {
      await navigator.clipboard.writeText(text);
      // 显示复制成功提示
      alert('已复制到剪贴板！');
    } catch (err) {
      console.error('复制失败:', err);
      // 降级方案：使用传统的复制方法
      try {
        const textArea = document.createElement('textarea');
        textArea.value = text;
        document.body.appendChild(textArea);
        textArea.select();
        document.execCommand('copy');
        document.body.removeChild(textArea);
        alert('已复制到剪贴板！');
      } catch (fallbackErr) {
        console.error('降级复制也失败:', fallbackErr);
        alert('复制失败，请手动复制文本');
      }
    }
  };

  const handleUsePrompt = (promptType) => {
    let textToUse = originalPrompt;
    if (promptType === 'optimized') {
      // 如果两个版本都有内容，让用户选择，否则使用有内容的版本
      if (displayedPromptA && displayedPromptB) {
        textToUse = displayedPromptA; // 默认使用Version A，可以改为让用户选择
      } else if (displayedPromptA) {
        textToUse = displayedPromptA;
      } else if (displayedPromptB) {
        textToUse = displayedPromptB;
      }
    }
    handleCopyToClipboard(textToUse);
    setShowEvaluationModal(false);
    // 可以在这里添加更多逻辑，比如显示成功提示
  };

  // 获取当前显示的文本
  const displayedPromptA = streamedTextA || '';
  const displayedPromptB = streamedTextB || '';

  // 计算进度百分比
  const getProgressPercentageA = () => {
    if (streamProgressA > 0) {
      return streamProgressA;
    }
    return isOptimizingA ? Math.floor(Math.random() * 30) + 10 : 0; // 随机10-40%的进度
  };

  const getProgressPercentageB = () => {
    if (streamProgressB > 0) {
      return streamProgressB;
    }
    return isOptimizingB ? Math.floor(Math.random() * 30) + 10 : 0; // 随机10-40%的进度
  };

  return (
    <Container>
      <ContentContainer>
        <OriginalPromptSection>
          <OriginalColumn>
            <OriginalHeader>
              <ColumnTitle>
                <IconWrapper>📝</IconWrapper>
                原始Prompt
              </ColumnTitle>
              <ActionButtonsGroup>
                <StorageStatusContainer>
                  <StorageStatusText>
                    存储: {storageBackendStatus?.currentBackend || '检查中...'}
                    {storageBackendStatus?.useElasticsearch ? ' 🟢' : ' 🟡'}
                    {storageBackendStatus?.simulationMode && ' (模拟)'}
                  </StorageStatusText>
                  <StorageCheckButton
                    onClick={handleCheckStorageBackend}
                    disabled={isCheckingBackend}
                  >
                    {isCheckingBackend ? <LoadingSpinner /> : '🔄'} 检查ES
                  </StorageCheckButton>
                  {storageBackendStatus?.simulationMode && (
                    <StorageCheckButton
                      onClick={handleToggleSimulation}
                      style={{ backgroundColor: storageBackendStatus?.simulateSuccess ? '#4caf50' : '#f44336' }}
                    >
                      🎭 {storageBackendStatus?.simulateSuccess ? '成功' : '失败'}
                    </StorageCheckButton>
                  )}
                </StorageStatusContainer>
                <AutoOptimizeButton
                  onClick={handleEvaluateOriginal}
                  disabled={isEvaluatingOriginal || !originalPrompt.trim()}
                >
                  {isEvaluatingOriginal ? <LoadingSpinner /> : <IconWrapper>📊</IconWrapper>} 评估原始Prompt
                </AutoOptimizeButton>
              </ActionButtonsGroup>
            </OriginalHeader>
            <OriginalEditorContainer>
              <OriginalLineNumbers>
                {lineNumbers.map(num => (
                  <div key={num}>{num}</div>
                ))}
              </OriginalLineNumbers>
              <OriginalTextArea
                value={originalPrompt}
                onChange={(e) => setOriginalPrompt(e.target.value)}
                placeholder="输入您的原始Prompt..."
                spellCheck="false"
              />
              {/* 添加token计数器 */}
              <TokenCounter>
                <IconWrapper>🔢</IconWrapper>
                {isCountingTokens ? '计算中...' : `${originalTokenCount} tokens`}
              </TokenCounter>
            </OriginalEditorContainer>
          </OriginalColumn>
        </OriginalPromptSection>

        <OptimizedVersionsContainer>
          <OptimizedColumn>
          <EnhancedHeader>
            <ColumnTitle>
              <IconWrapper>✨</IconWrapper>
              Prompt By AI Prompt Engeenier
              {isOptimizingA && <ProgressIndicator>增强中 {getProgressPercentageA()}%</ProgressIndicator>}
            </ColumnTitle>
            <ActionButtonsGroup>
              <AutoOptimizeButton
                onClick={handleAutoOptimizeA}
                disabled={isOptimizingA || isOptimizingB || !originalPrompt.trim()}
              >
                {isOptimizingA ? <LoadingSpinner /> : <IconWrapper>🔄</IconWrapper>} 优化
              </AutoOptimizeButton>
              <AutoOptimizeButton
                onClick={handleEvaluatePrompts}
                disabled={isEvaluating || !originalPrompt.trim() || !displayedPromptA.trim()}
              >
                {isEvaluating ? <LoadingSpinner /> : <IconWrapper>🔄</IconWrapper>} 对比评估
              </AutoOptimizeButton>
              <CopyButton
                onClick={() => handleCopyToClipboard(displayedPromptA)}
                title="复制到剪贴板"
                disabled={!displayedPromptA.trim()}
              >
                <IconWrapper>📋</IconWrapper> 复制
              </CopyButton>
            </ActionButtonsGroup>
          </EnhancedHeader>
          <EnhancedEditorContainer>
            <OptimizedContentWrapper>
              <div style={{ display: 'flex', flex: '1', minHeight: '200px' }}>
                <EnhancedLineNumbers>
                  {enhancedLineNumbersA.map(num => (
                    <div key={num}>{num}</div>
                  ))}
                </EnhancedLineNumbers>
                <EnhancedContent
                  className={isOptimizingA ? 'streaming' : (optimizationCompleteA ? 'complete' : '')}
                  style={{ overflow: 'visible' }} // Let the wrapper handle scrolling
                >
                  {errorA ? (
                    <ErrorMessage>
                      <strong>错误:</strong> {errorA}
                    </ErrorMessage>
                  ) : (
                    displayedPromptA || ''
                  )}
                </EnhancedContent>
                {/* 添加优化后的token计数器 */}
                {displayedPromptA && (
                  <EnhancedTokenCounter>
                    <IconWrapper>🔢</IconWrapper>
                    {`${enhancedTokenCountA} tokens`}
                  </EnhancedTokenCounter>
                )}
              </div>


            </OptimizedContentWrapper>
          </EnhancedEditorContainer>
        </OptimizedColumn>

          <OptimizedColumn>
          <EnhancedHeader>
            <ColumnTitle>
              <IconWrapper>⚡</IconWrapper>
              Prompt By Augment Code
              {isOptimizingB && <ProgressIndicator>增强中 {getProgressPercentageB()}%</ProgressIndicator>}
            </ColumnTitle>
            <ActionButtonsGroup>
              <AutoOptimizeButton
                onClick={handleAutoOptimizeB}
                disabled={isOptimizingA || isOptimizingB || !originalPrompt.trim()}
              >
                {isOptimizingB ? <LoadingSpinner /> : <IconWrapper>🔄</IconWrapper>} 优化
              </AutoOptimizeButton>
              {/* 暂时关闭引导优化功能 */}
              {/* <GuidedOptimizeButton
                onClick={() => setShowGuidanceModal(true)}
                disabled={isOptimizingA || isOptimizingB || !originalPrompt.trim()}
              >
                <IconWrapper>🧭</IconWrapper> 引导优化
              </GuidedOptimizeButton> */}
              <AutoOptimizeButton
                onClick={handleEvaluatePromptsB}
                disabled={isEvaluatingB || !originalPrompt.trim() || !displayedPromptB.trim()}
              >
                {isEvaluatingB ? <LoadingSpinner /> : <IconWrapper>�</IconWrapper>} 对比评估
              </AutoOptimizeButton>
              <CopyButton
                onClick={() => handleCopyToClipboard(displayedPromptB)}
                title="复制到剪贴板"
                disabled={!displayedPromptB.trim()}
              >
                <IconWrapper>📋</IconWrapper> 复制
              </CopyButton>
            </ActionButtonsGroup>
          </EnhancedHeader>
          <EnhancedEditorContainer>
            <OptimizedContentWrapper>
              <div style={{ display: 'flex', flex: '1', minHeight: '200px' }}>
                <EnhancedLineNumbers>
                  {enhancedLineNumbersB.map(num => (
                    <div key={num}>{num}</div>
                  ))}
                </EnhancedLineNumbers>
                <EnhancedContent
                  className={isOptimizingB ? 'streaming' : (optimizationCompleteB ? 'complete' : '')}
                  style={{ overflow: 'visible' }} // Let the wrapper handle scrolling
                >
                  {errorB ? (
                    <ErrorMessage>
                      <strong>错误:</strong> {errorB}
                    </ErrorMessage>
                  ) : (
                    displayedPromptB || ''
                  )}
                </EnhancedContent>
                {/* 添加优化后的token计数器 */}
                {displayedPromptB && (
                  <EnhancedTokenCounter>
                    <IconWrapper>🔢</IconWrapper>
                    {`${enhancedTokenCountB} tokens`}
                  </EnhancedTokenCounter>
                )}
              </div>


            </OptimizedContentWrapper>
          </EnhancedEditorContainer>
        </OptimizedColumn>
        </OptimizedVersionsContainer>
      </ContentContainer>

      {showGuidanceModal && (
        <Modal onClick={() => setShowGuidanceModal(false)}>
          <ModalContent onClick={(e) => e.stopPropagation()}>
            <ModalTitle>输入引导词</ModalTitle>
            <TextInput
              value={guidanceText}
              onChange={(e) => setGuidanceText(e.target.value)}
              placeholder="请输入引导词，如：增加角色定义、添加具体约束..."
              autoFocus
            />
            <ModalActions>
              <ModalCancelButton onClick={() => setShowGuidanceModal(false)}>
                取消
              </ModalCancelButton>
              <ModalConfirmButton
                onClick={handleGuidedOptimize}
                disabled={!guidanceText.trim()}
              >
                确认
              </ModalConfirmButton>
            </ModalActions>
          </ModalContent>
        </Modal>
      )}

      {showEvaluationModal && evaluationResult && (
        <EvaluationModal onClick={() => setShowEvaluationModal(false)}>
          <EvaluationModalContent onClick={(e) => e.stopPropagation()}>
            <EvaluationHeader>
              <EvaluationTitle>Prompt质量评估报告</EvaluationTitle>
              {isEvaluating && <LoadingSpinner />}
            </EvaluationHeader>

            {evaluationError ? (
              <ErrorMessage>
                <strong>评估失败:</strong> {evaluationError}
              </ErrorMessage>
            ) : evaluationResult.isSingleEvaluation ? (
              <>
                {/* 单个Prompt评估 */}
                <ComparisonContainer>
                  <PromptCard isWinner={true}>
                    <CardHeader>
                      <CardTitle>{evaluationResult.singleEvaluation.promptType}</CardTitle>
                    </CardHeader>

                    <TotalScore>
                      <TotalScoreValue>{evaluationResult.singleEvaluation.averageScore.toFixed(1)}</TotalScoreValue>
                      <TotalScoreLabel>平均分 (总分: {evaluationResult.singleEvaluation.totalScore}/80)</TotalScoreLabel>
                    </TotalScore>

                    <ScoreGrid>
                      <ScoreItem>
                        <span>清晰度</span>
                        <ScoreValue score={evaluationResult.singleEvaluation.scores.clarity}>
                          {evaluationResult.singleEvaluation.scores.clarity}/10
                        </ScoreValue>
                      </ScoreItem>
                      <ScoreItem>
                        <span>结构性</span>
                        <ScoreValue score={evaluationResult.singleEvaluation.scores.structure}>
                          {evaluationResult.singleEvaluation.scores.structure}/10
                        </ScoreValue>
                      </ScoreItem>
                      <ScoreItem>
                        <span>完整性</span>
                        <ScoreValue score={evaluationResult.singleEvaluation.scores.completeness}>
                          {evaluationResult.singleEvaluation.scores.completeness}/10
                        </ScoreValue>
                      </ScoreItem>
                      <ScoreItem>
                        <span>具体性</span>
                        <ScoreValue score={evaluationResult.singleEvaluation.scores.specificity}>
                          {evaluationResult.singleEvaluation.scores.specificity}/10
                        </ScoreValue>
                      </ScoreItem>
                      <ScoreItem>
                        <span>角色定义</span>
                        <ScoreValue score={evaluationResult.singleEvaluation.scores.roleDefinition}>
                          {evaluationResult.singleEvaluation.scores.roleDefinition}/10
                        </ScoreValue>
                      </ScoreItem>
                      <ScoreItem>
                        <span>输出格式</span>
                        <ScoreValue score={evaluationResult.singleEvaluation.scores.outputFormat}>
                          {evaluationResult.singleEvaluation.scores.outputFormat}/10
                        </ScoreValue>
                      </ScoreItem>
                      <ScoreItem>
                        <span>约束条件</span>
                        <ScoreValue score={evaluationResult.singleEvaluation.scores.constraints}>
                          {evaluationResult.singleEvaluation.scores.constraints}/10
                        </ScoreValue>
                      </ScoreItem>
                      <ScoreItem>
                        <span>可执行性</span>
                        <ScoreValue score={evaluationResult.singleEvaluation.scores.actionability}>
                          {evaluationResult.singleEvaluation.scores.actionability}/10
                        </ScoreValue>
                      </ScoreItem>
                    </ScoreGrid>

                    <AnalysisSection>
                      <AnalysisTitle>优势</AnalysisTitle>
                      <AnalysisList>
                        {evaluationResult.singleEvaluation.strengths.map((strength, index) => (
                          <li key={index}>{strength}</li>
                        ))}
                      </AnalysisList>
                    </AnalysisSection>

                    <AnalysisSection>
                      <AnalysisTitle>不足</AnalysisTitle>
                      <AnalysisList>
                        {evaluationResult.singleEvaluation.weaknesses.map((weakness, index) => (
                          <li key={index}>{weakness}</li>
                        ))}
                      </AnalysisList>
                    </AnalysisSection>

                    {evaluationResult.singleEvaluation.recommendations.length > 0 && (
                      <AnalysisSection>
                        <AnalysisTitle>改进建议</AnalysisTitle>
                        <AnalysisList>
                          {evaluationResult.singleEvaluation.recommendations.map((recommendation, index) => (
                            <li key={index}>{recommendation}</li>
                          ))}
                        </AnalysisList>
                      </AnalysisSection>
                    )}
                  </PromptCard>
                </ComparisonContainer>

                <EvaluationActions>
                  <UsePromptButton
                    onClick={() => handleCopyToClipboard(evaluationResult.singleEvaluation.prompt)}
                  >
                    复制Prompt
                  </UsePromptButton>
                  <CloseEvaluationButton onClick={() => setShowEvaluationModal(false)}>
                    关闭
                  </CloseEvaluationButton>
                </EvaluationActions>
              </>
            ) : (
              <>
                {/* 对比评估 */}
                <ComparisonContainer>
                  {/* 原始Prompt评估 */}
                  <PromptCard isWinner={evaluationResult.comparison.winner === 'original'}>
                    <CardHeader>
                      <CardTitle>原始Prompt</CardTitle>
                      {evaluationResult.comparison.winner === 'original' && (
                        <WinnerBadge>推荐使用</WinnerBadge>
                      )}
                    </CardHeader>

                    <TotalScore>
                      <TotalScoreValue>{evaluationResult.originalEvaluation.averageScore.toFixed(1)}</TotalScoreValue>
                      <TotalScoreLabel>平均分 (总分: {evaluationResult.originalEvaluation.totalScore}/80)</TotalScoreLabel>
                    </TotalScore>

                    <ScoreGrid>
                      <ScoreItem>
                        <span>清晰度</span>
                        <ScoreValue score={evaluationResult.originalEvaluation.scores.clarity}>
                          {evaluationResult.originalEvaluation.scores.clarity}/10
                        </ScoreValue>
                      </ScoreItem>
                      <ScoreItem>
                        <span>结构性</span>
                        <ScoreValue score={evaluationResult.originalEvaluation.scores.structure}>
                          {evaluationResult.originalEvaluation.scores.structure}/10
                        </ScoreValue>
                      </ScoreItem>
                      <ScoreItem>
                        <span>完整性</span>
                        <ScoreValue score={evaluationResult.originalEvaluation.scores.completeness}>
                          {evaluationResult.originalEvaluation.scores.completeness}/10
                        </ScoreValue>
                      </ScoreItem>
                      <ScoreItem>
                        <span>具体性</span>
                        <ScoreValue score={evaluationResult.originalEvaluation.scores.specificity}>
                          {evaluationResult.originalEvaluation.scores.specificity}/10
                        </ScoreValue>
                      </ScoreItem>
                      <ScoreItem>
                        <span>角色定义</span>
                        <ScoreValue score={evaluationResult.originalEvaluation.scores.roleDefinition}>
                          {evaluationResult.originalEvaluation.scores.roleDefinition}/10
                        </ScoreValue>
                      </ScoreItem>
                      <ScoreItem>
                        <span>输出格式</span>
                        <ScoreValue score={evaluationResult.originalEvaluation.scores.outputFormat}>
                          {evaluationResult.originalEvaluation.scores.outputFormat}/10
                        </ScoreValue>
                      </ScoreItem>
                      <ScoreItem>
                        <span>约束条件</span>
                        <ScoreValue score={evaluationResult.originalEvaluation.scores.constraints}>
                          {evaluationResult.originalEvaluation.scores.constraints}/10
                        </ScoreValue>
                      </ScoreItem>
                      <ScoreItem>
                        <span>可执行性</span>
                        <ScoreValue score={evaluationResult.originalEvaluation.scores.actionability}>
                          {evaluationResult.originalEvaluation.scores.actionability}/10
                        </ScoreValue>
                      </ScoreItem>
                    </ScoreGrid>

                    <AnalysisSection>
                      <AnalysisTitle>优势</AnalysisTitle>
                      <AnalysisList>
                        {evaluationResult.originalEvaluation.strengths.map((strength, index) => (
                          <li key={index}>{strength}</li>
                        ))}
                      </AnalysisList>
                    </AnalysisSection>

                    <AnalysisSection>
                      <AnalysisTitle>不足</AnalysisTitle>
                      <AnalysisList>
                        {evaluationResult.originalEvaluation.weaknesses.map((weakness, index) => (
                          <li key={index}>{weakness}</li>
                        ))}
                      </AnalysisList>
                    </AnalysisSection>
                  </PromptCard>

                  {/* 优化后Prompt评估 */}
                  <PromptCard isWinner={evaluationResult.comparison.winner === 'optimized'}>
                    <CardHeader>
                      <CardTitle>优化后Prompt</CardTitle>
                      {evaluationResult.comparison.winner === 'optimized' && (
                        <WinnerBadge>推荐使用</WinnerBadge>
                      )}
                    </CardHeader>

                    <TotalScore>
                      <TotalScoreValue>{evaluationResult.optimizedEvaluation.averageScore.toFixed(1)}</TotalScoreValue>
                      <TotalScoreLabel>平均分 (总分: {evaluationResult.optimizedEvaluation.totalScore}/80)</TotalScoreLabel>
                    </TotalScore>

                    <ScoreGrid>
                      <ScoreItem>
                        <span>清晰度</span>
                        <ScoreValue score={evaluationResult.optimizedEvaluation.scores.clarity}>
                          {evaluationResult.optimizedEvaluation.scores.clarity}/10
                        </ScoreValue>
                      </ScoreItem>
                      <ScoreItem>
                        <span>结构性</span>
                        <ScoreValue score={evaluationResult.optimizedEvaluation.scores.structure}>
                          {evaluationResult.optimizedEvaluation.scores.structure}/10
                        </ScoreValue>
                      </ScoreItem>
                      <ScoreItem>
                        <span>完整性</span>
                        <ScoreValue score={evaluationResult.optimizedEvaluation.scores.completeness}>
                          {evaluationResult.optimizedEvaluation.scores.completeness}/10
                        </ScoreValue>
                      </ScoreItem>
                      <ScoreItem>
                        <span>具体性</span>
                        <ScoreValue score={evaluationResult.optimizedEvaluation.scores.specificity}>
                          {evaluationResult.optimizedEvaluation.scores.specificity}/10
                        </ScoreValue>
                      </ScoreItem>
                      <ScoreItem>
                        <span>角色定义</span>
                        <ScoreValue score={evaluationResult.optimizedEvaluation.scores.roleDefinition}>
                          {evaluationResult.optimizedEvaluation.scores.roleDefinition}/10
                        </ScoreValue>
                      </ScoreItem>
                      <ScoreItem>
                        <span>输出格式</span>
                        <ScoreValue score={evaluationResult.optimizedEvaluation.scores.outputFormat}>
                          {evaluationResult.optimizedEvaluation.scores.outputFormat}/10
                        </ScoreValue>
                      </ScoreItem>
                      <ScoreItem>
                        <span>约束条件</span>
                        <ScoreValue score={evaluationResult.optimizedEvaluation.scores.constraints}>
                          {evaluationResult.optimizedEvaluation.scores.constraints}/10
                        </ScoreValue>
                      </ScoreItem>
                      <ScoreItem>
                        <span>可执行性</span>
                        <ScoreValue score={evaluationResult.optimizedEvaluation.scores.actionability}>
                          {evaluationResult.optimizedEvaluation.scores.actionability}/10
                        </ScoreValue>
                      </ScoreItem>
                    </ScoreGrid>

                    <AnalysisSection>
                      <AnalysisTitle>优势</AnalysisTitle>
                      <AnalysisList>
                        {evaluationResult.optimizedEvaluation.strengths.map((strength, index) => (
                          <li key={index}>{strength}</li>
                        ))}
                      </AnalysisList>
                    </AnalysisSection>

                    <AnalysisSection>
                      <AnalysisTitle>不足</AnalysisTitle>
                      <AnalysisList>
                        {evaluationResult.optimizedEvaluation.weaknesses.map((weakness, index) => (
                          <li key={index}>{weakness}</li>
                        ))}
                      </AnalysisList>
                    </AnalysisSection>
                  </PromptCard>
                </ComparisonContainer>

                <RecommendationSection>
                  <RecommendationTitle>专家建议</RecommendationTitle>
                  <RecommendationText>{evaluationResult.comparison.recommendation}</RecommendationText>
                </RecommendationSection>

                {/* 显示优化点信息 */}
                {evaluationResult.optimizationImprovements && (
                  <RecommendationSection>
                    <RecommendationTitle>
                      发现的优化点 (Version {evaluationResult.optimizationImprovements.versionUsed})
                    </RecommendationTitle>
                    <AnalysisList>
                      {(evaluationResult.optimizationImprovements.versionA || evaluationResult.optimizationImprovements.versionB || []).map((item, index) => (
                        <li key={index}>
                          <strong>{item.type || item.aspect}:</strong> {item.description}
                        </li>
                      ))}
                    </AnalysisList>
                  </RecommendationSection>
                )}

                <EvaluationActions>
                  <UsePromptButton
                    onClick={() => handleUsePrompt(evaluationResult.comparison.winner)}
                  >
                    使用{evaluationResult.comparison.winner === 'original' ? '原始' : '优化后'}Prompt
                  </UsePromptButton>
                  <CloseEvaluationButton onClick={() => setShowEvaluationModal(false)}>
                    关闭
                  </CloseEvaluationButton>
                </EvaluationActions>
              </>
            )}
          </EvaluationModalContent>
        </EvaluationModal>
      )}
    </Container>
  );
};

export default PromptOptimizer; 