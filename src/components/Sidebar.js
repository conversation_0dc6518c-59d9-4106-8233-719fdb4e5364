import React, { useState } from 'react';
import styled from 'styled-components';
import { FaChevronLeft, FaChevronRight } from 'react-icons/fa';

const SidebarContainer = styled.div`
  display: flex;
  flex-direction: column;
  width: ${props => props.collapsed ? '80px' : '270px'};
  min-width: ${props => props.collapsed ? '80px' : '270px'};
  background-color: ${props => props.theme.surface};
  border-right: 1px solid ${props => props.theme.borderLight};
  padding: 2rem 0;
  box-shadow: 1px 0 10px rgba(0, 0, 0, 0.07);
  transition: all 0.3s ease;
  position: relative;
  z-index: 5;
  
  &::before {
    content: '';
    position: absolute;
    top: 0;
    right: 0;
    width: 1px;
    height: 100%;
    background: linear-gradient(
      to bottom,
      transparent,
      ${props => props.theme.primary}20,
      transparent
    );
  }
`;

const SidebarHeader = styled.div`
  padding: 0 1.75rem;
  margin-bottom: 2.5rem;
`;

const SidebarTitle = styled.h2`
  margin: 0 0 0.75rem 0;
  font-size: 1.5rem;
  color: ${props => props.theme.primary};
  font-weight: 700;
  letter-spacing: 0.5px;
  position: relative;
  display: flex;
  align-items: center;
`;

const SidebarSubtitle = styled.div`
  color: ${props => props.theme.textSecondary};
  font-size: 0.85rem;
  font-weight: 500;
  letter-spacing: 0.3px;
  margin-left: 0.25rem;
`;

const Divider = styled.div`
  height: 1px;
  background: linear-gradient(
    to right,
    ${props => props.theme.primary}40,
    transparent
  );
  margin: 1rem 0 1.5rem 1.75rem;
  width: 60%;
`;

const SidebarMenu = styled.div`
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
  padding: 0 1rem;
`;

const SidebarItem = styled.div`
  padding: 0.75rem ${props => props.collapsed ? '0.5rem' : '1.5rem'};
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 1rem;
  color: ${props => props.active ? props.theme.primary : props.theme.text};
  background-color: ${props => props.active ? props.theme.primaryLight : 'transparent'};
  font-weight: ${props => props.active ? '600' : '500'};
  border-radius: 8px;
  transition: all 0.2s ease;
  position: relative;
  overflow: hidden;
  justify-content: ${props => props.collapsed ? 'center' : 'flex-start'};

  &:hover {
    background-color: ${props => props.active ? props.theme.primaryLight : props.theme.borderLight};
    transform: translateX(3px);
  }
  
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: ${props => props.active ? '0' : '-100%'};
    width: 5px;
    height: 100%;
    background: ${props => props.theme.primary};
    opacity: ${props => props.active ? 1 : 0};
    transition: all 0.3s ease;
    border-top-right-radius: 3px;
    border-bottom-right-radius: 3px;
  }
  
  &:hover::before {
    opacity: ${props => props.active ? 1 : 0.5};
    left: 0;
  }
`;

const IconWrapper = styled.div`
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.3rem;
  width: 2.25rem;
  height: 2.25rem;
  border-radius: 10px;
  background-color: ${props => props.active ? props.theme.primary + '20' : props.theme.borderLight};
  padding: 0.3rem;
  transition: all 0.2s ease;
  box-shadow: ${props => props.active ? `0 3px 8px ${props.theme.primary}30` : 'none'};
  
  &::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 10px;
    height: 10px;
    background: ${props => props.theme.primary}20;
    border-radius: 50%;
    transform: translate(-50%, -50%);
    opacity: 0;
    transition: all 0.3s ease;
  }
`;

const ItemLabel = styled.span`
  z-index: 1;
  font-size: 0.95rem;
  letter-spacing: 0.3px;
  transition: transform 0.2s ease;
  display: ${props => props.collapsed ? 'none' : 'block'};
`;

const ActiveIndicator = styled.div`
  position: absolute;
  right: 1rem;
  width: 6px;
  height: 6px;
  border-radius: 50%;
  background-color: ${props => props.theme.primary};
  opacity: ${props => props.active && !props.collapsed ? 1 : 0};
  transition: all 0.2s ease;
`;

const CollapseButton = styled.button`
  position: absolute;
  right: -12px;
  top: 20px;
  width: 24px;
  height: 24px;
  border-radius: 50%;
  background-color: ${props => props.theme.surface};
  border: 1px solid ${props => props.theme.borderLight};
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  color: ${props => props.theme.primary};
  padding: 0;
  z-index: 10;
  transition: all 0.2s ease;
  
  &:hover {
    background-color: ${props => props.theme.primaryLight};
    transform: scale(1.1);
  }
  
  svg {
    font-size: 10px;
  }
`;

const Sidebar = ({ activeModule, onModuleChange }) => {
  const [collapsed, setCollapsed] = useState(false);
  
  const toggleCollapsed = () => {
    setCollapsed(!collapsed);
  };

  const modules = [
    { id: 'new-project', label: '新项目Prompt生成', icon: '🚀' },
    { id: 'prompt-optimizer', label: 'Code Prompt优化', icon: '🪄' },
    { id: 'prompt-analysis', label: 'Prompt分析中心', icon: '📊' }
  ];

  return (
    <SidebarContainer collapsed={collapsed}>
      <CollapseButton onClick={toggleCollapsed}>
        {collapsed ? <FaChevronRight /> : <FaChevronLeft />}
      </CollapseButton>
      <SidebarMenu>
        {modules.map(module => (
          <SidebarItem
            key={module.id}
            active={activeModule === module.id}
            onClick={() => onModuleChange(module.id)}
            collapsed={collapsed}
          >
            <IconWrapper active={activeModule === module.id}>{module.icon}</IconWrapper>
            <ItemLabel collapsed={collapsed}>{module.label}</ItemLabel>
            <ActiveIndicator active={activeModule === module.id} collapsed={collapsed} />
          </SidebarItem>
        ))}
      </SidebarMenu>
    </SidebarContainer>
  );
};

export default Sidebar; 