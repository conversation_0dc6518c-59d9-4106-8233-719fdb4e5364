import React from 'react';
import styled from 'styled-components';

const FooterContainer = styled.footer`
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 12px 20px;
  border-top: 1px solid ${props => props.theme.border};
  margin-top: auto;
  background-color: ${props => props.theme.surface};
  transition: all 0.3s ease;
`;

const FooterContent = styled.div`
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 4px;
  max-width: 1200px;
  width: 100%;
`;

const FooterText = styled.div`
  color: ${props => props.theme.textMuted};
  font-size: 12px;
  text-align: center;
  line-height: 1.5;
`;

const TeamName = styled.span`
  color: ${props => props.theme.primary};
  font-weight: 500;
`;

const VersionInfo = styled.div`
  color: ${props => props.theme.textMuted};
  font-size: 11px;
  opacity: 0.8;
`;

const Footer = () => {
  const currentYear = new Date().getFullYear();
  const version = 'v1.2.0'; // 可以从环境变量或配置文件中获取

  return (
    <FooterContainer>
      <FooterContent>
        <FooterText>
          © {currentYear} Supported by <TeamName>商业增值技术部-商家及运营系统组-供给增长系统组</TeamName>
        </FooterText>
        <VersionInfo>Repository Design {version}</VersionInfo>
      </FooterContent>
    </FooterContainer>
  );
};

export default Footer; 