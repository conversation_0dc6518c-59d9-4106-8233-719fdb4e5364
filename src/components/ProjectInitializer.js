import React, { useState } from 'react';
import styled from 'styled-components';
import { FaRobot, FaTimes, FaGithub, FaSyncAlt } from 'react-icons/fa';
import { promptManager, PROJECT_TYPES, PROJECT_TYPE_LABELS } from '../prompts';
import { getLLMJsonResponse, streamJsonResponse } from '../services/llmService';
import {
  createProjectStructurePrompt,
  createStructureFromRepoPrompt,
  createStructureFromFolderPrompt
} from '../prompts/projectStructure';

const FloatingWindow = styled.div`
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
`;

const WindowContent = styled.div`
  width: 600px;
  max-width: 90%;
  background-color: #ffffff;
  border-radius: 8px;
  padding: 24px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
`;

const Header = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
`;

const Title = styled.h2`
  color: #3182ce;
  margin: 0;
`;

const CloseButton = styled.button`
  background: none;
  border: none;
  color: #718096;
  font-size: 20px;
  cursor: pointer;
  padding: 4px;
  &:hover {
    color: #2d3748;
  }
`;

const Form = styled.form`
  display: flex;
  flex-direction: column;
  gap: 16px;
`;

const FormGroup = styled.div`
  display: flex;
  flex-direction: column;
  gap: 8px;
`;

const Label = styled.label`
  font-weight: 500;
  color: #4a5568;
`;

const TextArea = styled.textarea`
  padding: 12px;
  border: 1px solid #e2e8f0;
  border-radius: 4px;
  font-size: 14px;
  min-height: 100px;
  resize: vertical;
  &:focus {
    outline: none;
    border-color: #3182ce;
  }
`;

const Select = styled.select`
  padding: 12px;
  border: 1px solid #e2e8f0;
  border-radius: 4px;
  font-size: 14px;
  &:focus {
    outline: none;
    border-color: #3182ce;
  }
`;

const Input = styled.input`
  padding: 12px;
  border: 1px solid #e2e8f0;
  border-radius: 4px;
  font-size: 14px;
  &:focus {
    outline: none;
    border-color: #3182ce;
  }
`;

const GenerateButton = styled.button`
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  background-color: #3182ce;
  color: white;
  border: none;
  border-radius: 4px;
  padding: 12px 24px;
  font-size: 16px;
  font-weight: 500;
  cursor: pointer;
  margin-top: 16px;
  transition: background-color 0.2s;
  &:hover {
    background-color: #2c5282;
  }
  &:disabled {
    background-color: #a0aec0;
    cursor: not-allowed;
  }
`;

const LoadingIndicator = styled.div`
  display: inline-block;
  width: 20px;
  height: 20px;
  border: 3px solid rgba(255, 255, 255, 0.3);
  border-radius: 50%;
  border-top-color: #fff;
  animation: spin 1s ease-in-out infinite;

  @keyframes spin {
    to {
      transform: rotate(360deg);
    }
  }
`;

const ErrorMessage = styled.div`
  color: #e53e3e;
  font-size: 14px;
  margin-top: 8px;
`;

const InputGroup = styled.div`
  display: flex;
  gap: 8px;
`;

const LeftInput = styled.input`
  flex: 1;
  padding: 12px;
  border: 1px solid #e2e8f0;
  border-radius: 4px;
  font-size: 14px;
  &:focus {
    outline: none;
    border-color: #3182ce;
  }
`;

const Button = styled.button`
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 6px;
  background-color: #3182ce;
  color: white;
  border: none;
  border-radius: 4px;
  padding: 8px 12px;
  font-size: 14px;
  cursor: pointer;
  transition: background-color 0.2s;
  &:hover {
    background-color: #2c5282;
  }
  &:disabled {
    background-color: #a0aec0;
    cursor: not-allowed;
  }
`;

const RepoStructure = styled.div`
  background-color: #f8f9fa;
  border: 1px solid #e2e8f0;
  border-radius: 4px;
  padding: 12px;
  margin-top: 8px;
  max-height: 150px;
  overflow-y: auto;
  font-family: monospace;
  font-size: 13px;
  white-space: pre-wrap;
  color: #4a5568;
`;

const FolderUploadArea = styled.div`
  border: 2px dashed #cbd5e0;
  border-radius: 8px;
  padding: 20px;
  text-align: center;
  margin-top: 8px;
  background-color: #f7fafc;
  transition: all 0.2s ease;
  cursor: pointer;

  &:hover {
    border-color: #4299e1;
    background-color: #ebf8ff;
  }

  &.dragover {
    border-color: #3182ce;
    background-color: #bee3f8;
  }
`;

const FolderUploadText = styled.div`
  color: #4a5568;
  font-size: 14px;
  margin-bottom: 8px;
`;

const FolderUploadHint = styled.div`
  color: #718096;
  font-size: 12px;
`;

const FolderStructurePreview = styled.div`
  background-color: #f8f9fa;
  border: 1px solid #e2e8f0;
  border-radius: 4px;
  padding: 12px;
  margin-top: 8px;
  max-height: 150px;
  overflow-y: auto;
  font-family: monospace;
  font-size: 13px;
  white-space: pre-wrap;
  color: #4a5568;
`;

const MethodSelector = styled.div`
  display: flex;
  gap: 8px;
  margin-bottom: 16px;
  border-bottom: 1px solid #e2e8f0;
  padding-bottom: 16px;
`;

const MethodTab = styled.button`
  padding: 8px 16px;
  border: 1px solid #e2e8f0;
  border-radius: 6px;
  background-color: ${props => props.active ? '#4299e1' : '#ffffff'};
  color: ${props => props.active ? '#ffffff' : '#4a5568'};
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;

  &:hover {
    background-color: ${props => props.active ? '#3182ce' : '#f7fafc'};
  }

  &:disabled {
    opacity: 0.6;
    cursor: not-allowed;
  }
`;

// 增加流式处理UI组件
const StreamingContainer = styled.div`
  margin-top: 16px;
  border-radius: 8px;
  border: 1px solid #e2e8f0;
  background-color: #f8f9fa;
  overflow: hidden;
  display: ${props => props.visible ? 'block' : 'none'};
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
`;

const StreamingHeader = styled.div`
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 10px 12px;
  background-color: #edf2f7;
  border-bottom: 1px solid #e2e8f0;
`;

const StreamingTitle = styled.div`
  font-size: 14px;
  font-weight: 500;
  color: #4a5568;
  display: flex;
  align-items: center;
  gap: 6px;
`;

const StreamingBadge = styled.span`
  display: inline-flex;
  align-items: center;
  padding: 3px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;
  background-color: ${props => {
    switch(props.status) {
      case 'generating': return '#ebf8ff';
      case 'parsing': return '#fefcbf';
      case 'complete': return '#c6f6d5';
      case 'error': return '#fed7d7';
      default: return '#e2e8f0';
    }
  }};
  color: ${props => {
    switch(props.status) {
      case 'generating': return '#3182ce';
      case 'parsing': return '#d69e2e';
      case 'complete': return '#38a169';
      case 'error': return '#e53e3e';
      default: return '#718096';
    }
  }};
`;

const StreamingContent = styled.div`
  padding: 16px;
  font-size: 13px;
  color: #4a5568;
  max-height: 200px;
  overflow-y: auto;
  white-space: pre-wrap;
  font-family: monospace;
  line-height: 1.5;
  background-color: #fff;
`;

const ProgressBar = styled.div`
  height: 6px;
  background-color: #e2e8f0;
  border-radius: 3px;
  overflow: hidden;
  margin: 0 16px 12px;
`;

const ProgressIndicator = styled.div`
  height: 100%;
  background-color: ${props => {
    if (props.percent >= 100) return '#38a169';
    if (props.percent >= 70) return '#3182ce';
    if (props.percent >= 30) return '#d69e2e';
    return '#718096';
  }};
  width: ${props => props.percent}%;
  transition: width 0.3s ease, background-color 0.5s ease;
`;

const GenerationStatus = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 16px 16px;
`;

const StatusText = styled.div`
  font-size: 12px;
  color: #718096;
`;

const ProgressText = styled.div`
  font-size: 12px;
  font-weight: 500;
  color: #4a5568;
`;

// GitHub API响应转换为树结构
const formatGitHubTreeToProjectStructure = (tree, rootName) => {
  // 按路径排序以确保父目录在子目录之前
  tree.sort((a, b) => a.path.localeCompare(b.path));
  
  // 创建一个映射来跟踪文件夹
  const folders = {};
  
  // 创建root文件夹
  const root = {
    name: rootName,
    type: 'folder',
    capabilities: '根目录',
    children: []
  };
  
  folders[''] = root;
  
  // 处理每个文件和文件夹
  tree.forEach(item => {
    // 跳过一些常见的不需要包含的文件
    if (item.path.includes('.git/') || item.path === '.git' || 
        item.path.includes('node_modules/') || 
        item.path === '.DS_Store') {
      return;
    }
    
    const isBlob = item.type === 'blob';
    const isTree = item.type === 'tree';
    
    if (!isBlob && !isTree) return; // 跳过其他类型
    
    const pathParts = item.path.split('/');
    const name = pathParts.pop(); // 获取文件或文件夹名称
    const parentPath = pathParts.join('/');
    
    // 确保父文件夹存在
    if (!folders[parentPath] && parentPath) {
      // 如果父文件夹不存在，创建缺失的父文件夹
      let currentPath = '';
      pathParts.forEach(part => {
        const prevPath = currentPath;
        currentPath = currentPath ? `${currentPath}/${part}` : part;
        
        if (!folders[currentPath]) {
          const newFolder = {
            name: part,
            type: 'folder',
            capabilities: `${part}目录`,
            children: []
          };
          
          folders[currentPath] = newFolder;
          folders[prevPath].children.push(newFolder);
        }
      });
    }
    
    const parentFolder = folders[parentPath] || root;
    
    if (isTree) {
      // 创建文件夹
      const folder = {
        name: name,
        type: 'folder',
        capabilities: `${name}目录`,
        children: []
      };
      
      folders[item.path] = folder;
      parentFolder.children.push(folder);
    } else if (isBlob) {
      // 创建文件
      let capabilities = `${name}文件`;
      
      // 根据文件扩展名推断文件功能
      if (name === 'package.json') capabilities = '项目依赖配置';
      else if (name === 'requirements.txt') capabilities = 'Python依赖配置';
      else if (name === 'README.md') capabilities = '项目说明文档';
      else if (name.endsWith('.py')) capabilities = 'Python源代码';
      else if (name.endsWith('.js')) capabilities = 'JavaScript源代码';
      else if (name.endsWith('.jsx') || name.endsWith('.tsx')) capabilities = 'React组件';
      else if (name.endsWith('.vue')) capabilities = 'Vue组件';
      else if (name.endsWith('.java')) capabilities = 'Java源代码';
      else if (name.endsWith('.html')) capabilities = 'HTML文件';
      else if (name.endsWith('.css')) capabilities = 'CSS样式表';
      else if (name.endsWith('.json')) capabilities = 'JSON配置文件';
      else if (name.endsWith('.yml') || name.endsWith('.yaml')) capabilities = '配置文件';
      
      const file = {
        name: name,
        type: 'file',
        capabilities: capabilities
      };
      
      parentFolder.children.push(file);
    }
  });
  
  return root;
};

// 提取GitHub仓库所有者和名称
const extractRepoInfo = (url) => {
  const regex = /github\.com\/([^\/]+)\/([^\/]+)/;
  const match = url.match(regex);
  
  if (!match) {
    throw new Error('无效的GitHub仓库URL');
  }
  
  return {
    owner: match[1],
    repo: match[2].replace('.git', '')
  };
};

const ProjectInitializer = ({ onClose, onGenerate }) => {
  const [description, setDescription] = useState('');
  const [projectType, setProjectType] = useState(PROJECT_TYPES.LLM_PYTHON);
  const [additionalInfo, setAdditionalInfo] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState('');
  
  // 流式处理相关状态
  const [streamingVisible, setStreamingVisible] = useState(false);
  const [streamingContent, setStreamingContent] = useState('');
  const [streamProgress, setStreamProgress] = useState(0);
  const [streamStatus, setStreamStatus] = useState('');
  
  // 生成方式选择
  const [generationMethod, setGenerationMethod] = useState('basic'); // 'basic', 'repo', 'folder'

  // GitHub相关状态
  const [repoUrl, setRepoUrl] = useState('');
  const [repoLoading, setRepoLoading] = useState(false);
  const [repoError, setRepoError] = useState('');
  const [repoStructure, setRepoStructure] = useState(null);
  const [repoStructurePreview, setRepoStructurePreview] = useState('');

  // 文件夹上传相关状态
  const [folderStructure, setFolderStructure] = useState(null);
  const [folderStructurePreview, setFolderStructurePreview] = useState('');
  const [folderError, setFolderError] = useState('');

  // 从GitHub获取仓库结构
  const fetchGitHubRepoStructure = async () => {
    if (!repoUrl.trim()) {
      setRepoError('请输入GitHub仓库链接');
      return;
    }
    
    setRepoLoading(true);
    setRepoError('');
    
    try {
      // 从URL中提取仓库所有者和名称
      const { owner, repo } = extractRepoInfo(repoUrl);
      
      // 使用GitHub API获取仓库内容
      const response = await fetch(`https://api.github.com/repos/${owner}/${repo}/git/trees/main?recursive=1`);
      
      if (!response.ok) {
        // 如果main分支不存在，尝试master分支
        const masterResponse = await fetch(`https://api.github.com/repos/${owner}/${repo}/git/trees/master?recursive=1`);
        
        if (!masterResponse.ok) {
          throw new Error('无法获取仓库结构，请确认仓库URL是否正确且为公开仓库');
        }
        
        const data = await masterResponse.json();
        processGitHubResponse(data, repo);
      } else {
        const data = await response.json();
        processGitHubResponse(data, repo);
      }
    } catch (err) {
      console.error('获取GitHub仓库结构错误:', err);
      setRepoError(err.message || '无法获取仓库结构');
    } finally {
      setRepoLoading(false);
    }
  };
  
  // 处理GitHub API响应
  const processGitHubResponse = (data, repoName) => {
    if (!data.tree) {
      setRepoError('仓库结构不可用');
      return;
    }
    
    // 转换为我们的项目结构格式
    const structure = formatGitHubTreeToProjectStructure(data.tree, repoName);
    setRepoStructure(structure);
    
    // 创建一个简单的预览
    const previewLines = [];
    
    const generatePreview = (node, depth = 0) => {
      const indent = '  '.repeat(depth);
      const prefix = depth > 0 ? (depth > 1 ? '└─ ' : '├─ ') : '';
      
      previewLines.push(`${indent}${prefix}${node.name} ${node.type === 'folder' ? '📁' : '📄'}`);
      
      if (node.children && node.children.length > 0) {
        node.children.slice(0, 5).forEach(child => {
          generatePreview(child, depth + 1);
        });
        
        if (node.children.length > 5) {
          previewLines.push(`${indent}  └─ ... (${node.children.length - 5} more items)`);
        }
      }
    };
    
    generatePreview(structure);
    setRepoStructurePreview(previewLines.join('\n'));
  };
  
  // 分配唯一ID
  const assignIds = (node, parentId = 'root', idCounter = { value: 0 }) => {
    // 为当前节点分配ID
    // 如果是根节点，ID就是'root'
    // 否则使用格式: 父ID + '_' + 计数器，例如 'root_0', 'root_0_1'
    const currentId = parentId === 'root' ? 'root' : `${parentId}_${idCounter.value}`;
    idCounter.value++; // 递增计数器

    const newNode = {
      ...node,
      id: currentId
    };

    // 递归处理子节点
    if (node.children && Array.isArray(node.children)) {
      // 为子节点创建新的计数器，这样每个级别的节点都有从0开始的计数器
      const childCounter = { value: 0 };
      newNode.children = node.children.map(child =>
        assignIds(child, currentId, childCounter)
      );
    }

    return newNode;
  };

  // 处理文件夹上传
  const handleFolderUpload = (event) => {
    const files = event.target.files;
    if (!files || files.length === 0) return;

    setFolderError('');
    processFolderFiles(files);
  };

  // 处理拖拽上传
  const handleFolderDrop = (event) => {
    event.preventDefault();
    event.stopPropagation();

    const items = event.dataTransfer.items;
    if (!items) return;

    setFolderError('');

    // 处理拖拽的文件夹
    const files = [];
    for (let i = 0; i < items.length; i++) {
      const item = items[i];
      if (item.kind === 'file') {
        const file = item.getAsFile();
        if (file) files.push(file);
      }
    }

    if (files.length > 0) {
      processFolderFiles(files);
    }
  };

  // 处理文件夹文件列表
  const processFolderFiles = async (files) => {
    try {
      console.log('📁 开始处理文件夹，原始文件数:', files.length);
      console.log('📋 原始文件列表（前10个）:');
      Array.from(files).slice(0, 10).forEach((file, index) => {
        console.log(`  ${index + 1}. ${file.webkitRelativePath || file.name}`);
      });
      if (files.length > 10) {
        console.log(`  ... 还有 ${files.length - 10} 个文件`);
      }

      // 应用 .gitignore 过滤
      const filteredFiles = await applyGitignoreFilter(files);

      console.log('✅ 过滤完成，过滤后文件数:', filteredFiles.length);

      // 转换文件列表为项目结构
      const structure = convertFilesToStructure(filteredFiles);
      setFolderStructure(structure);

      console.log('🏗️ 项目结构生成完成');
      console.log('📊 根目录子项数:', structure.children ? structure.children.length : 0);
      console.log('📁 根目录子项详情:');
      if (structure.children) {
        structure.children.forEach((child, index) => {
          console.log(`  ${index + 1}. ${child.name} (${child.type})`);
        });
      }

      // 生成预览
      const previewLines = [];
      const generateFolderPreview = (node, depth = 0) => {
        const indent = '  '.repeat(depth);
        const prefix = depth > 0 ? (depth > 1 ? '└─ ' : '├─ ') : '';

        previewLines.push(`${indent}${prefix}${node.name} ${node.type === 'folder' ? '📁' : '📄'}`);

        if (node.children && node.children.length > 0) {
          // 增加显示数量，并且优先显示文件夹
          const folders = node.children.filter(child => child.type === 'folder');
          const files = node.children.filter(child => child.type === 'file');

          // 显示所有文件夹（最多8个）
          folders.slice(0, 8).forEach(child => {
            generateFolderPreview(child, depth + 1);
          });

          // 显示部分文件（最多5个）
          files.slice(0, 5).forEach(child => {
            generateFolderPreview(child, depth + 1);
          });

          // 显示省略信息
          const totalHidden = Math.max(0, folders.length - 8) + Math.max(0, files.length - 5);
          if (totalHidden > 0) {
            previewLines.push(`${indent}  └─ ... (${totalHidden} more items)`);
          }
        }
      };

      generateFolderPreview(structure);
      setFolderStructurePreview(previewLines.join('\n'));

    } catch (error) {
      console.error('处理文件夹结构错误:', error);
      setFolderError('无法解析文件夹结构，请确保选择了有效的项目文件夹');
    }
  };

  // 解析 .gitignore 文件内容
  const parseGitignore = (content) => {
    const rules = [];
    const lines = content.split('\n');

    for (const line of lines) {
      const trimmed = line.trim();

      // 跳过空行和注释
      if (!trimmed || trimmed.startsWith('#')) {
        continue;
      }

      // 处理否定规则（以!开头）
      const isNegation = trimmed.startsWith('!');
      const pattern = isNegation ? trimmed.slice(1) : trimmed;

      // 转换 gitignore 模式为正则表达式
      const regex = gitignorePatternToRegex(pattern);

      rules.push({
        pattern,
        regex,
        isNegation
      });
    }

    return rules;
  };

  // 将 gitignore 模式转换为正则表达式
  const gitignorePatternToRegex = (pattern) => {
    // 转义特殊字符，但保留 * 和 ?
    let regexPattern = pattern
      .replace(/[.+^${}()|[\]\\]/g, '\\$&')  // 转义正则特殊字符
      .replace(/\\\*/g, '.*')                // * 匹配任意字符
      .replace(/\\\?/g, '.');                // ? 匹配单个字符

    // 处理目录匹配
    if (pattern.endsWith('/')) {
      regexPattern = regexPattern.slice(0, -1) + '($|/.*)';
    } else if (pattern.includes('/')) {
      // 包含路径分隔符，精确匹配路径
      regexPattern = '^' + regexPattern + '($|/.*)';
    } else {
      // 不包含路径分隔符，匹配任何位置的文件/文件夹
      regexPattern = '(^|/)' + regexPattern + '($|/.*)';
    }

    return new RegExp(regexPattern);
  };

  // 检查文件是否应该被忽略
  const shouldIgnoreFile = (filePath, gitignoreRules) => {
    let shouldIgnore = false;

    for (const rule of gitignoreRules) {
      if (rule.regex.test(filePath)) {
        shouldIgnore = !rule.isNegation;
      }
    }

    return shouldIgnore;
  };

  // 应用忽略规则过滤文件
  const applyIgnoreRules = (fileArray, rules) => {
    // 获取根目录名称
    const rootName = fileArray.length > 0 ?
      fileArray[0].webkitRelativePath.split('/')[0] : '';

    console.log('🔍 开始应用过滤规则，根目录名称:', rootName);
    console.log('📋 应用的规则数量:', rules.length);

    // 过滤文件
    const filteredFiles = fileArray.filter(file => {
      const fullPath = file.webkitRelativePath || file.name;
      // 移除根目录前缀来匹配忽略规则
      const relativePath = rootName ? fullPath.replace(rootName + '/', '') : fullPath;

      const ignored = shouldIgnoreFile(relativePath, rules);
      if (ignored) {
        console.log('🚫 忽略文件:', relativePath);
      }
      return !ignored;
    });

    console.log(`📊 过滤统计: ${fileArray.length} → ${filteredFiles.length} (减少了 ${fileArray.length - filteredFiles.length} 个文件)`);
    return filteredFiles;
  };

  // 默认忽略规则（即使没有 .gitignore 文件也会应用）
  const getDefaultIgnoreRules = () => {
    const defaultPatterns = [
      '.git/',
      'node_modules/',
      '.DS_Store',
      'Thumbs.db',
      '*.log',
      '.env',
      '.env.local',
      '.env.*.local',
      'dist/',
      'build/',
      '.cache/',
      '.vscode/',
      '.idea/',
      '*.swp',
      '*.swo',
      '*~',
      '__pycache__/',
      '*.pyc',
      '*.pyo',
      '*.pyd',
      '.Python',
      'venv/',
      'env/',
      '.venv/',
      '.coverage',
      '.pytest_cache/',
      '.mypy_cache/',
      'target/',
      '*.class',
      '*.jar',
      '*.war',
      '*.ear'
    ];

    return defaultPatterns.map(pattern => ({
      pattern,
      regex: gitignorePatternToRegex(pattern),
      isNegation: false
    }));
  };

  // 应用 .gitignore 过滤
  const applyGitignoreFilter = (files) => {
    const fileArray = Array.from(files);

    // 查找 .gitignore 文件
    const gitignoreFile = fileArray.find(file => {
      const path = file.webkitRelativePath || file.name;
      return path.endsWith('/.gitignore') || path === '.gitignore';
    });

    // 获取默认忽略规则
    const defaultRules = getDefaultIgnoreRules();

    if (!gitignoreFile) {
      console.log('未找到 .gitignore 文件，使用默认忽略规则');
      return applyIgnoreRules(fileArray, defaultRules);
    }

    // 读取 .gitignore 内容
    return new Promise((resolve) => {
      const reader = new FileReader();
      reader.onload = (e) => {
        try {
          const gitignoreContent = e.target.result;
          const gitignoreRules = parseGitignore(gitignoreContent);

          console.log('解析到的 .gitignore 规则:', gitignoreRules.map(r => r.pattern));

          // 合并默认规则和 .gitignore 规则
          const allRules = [...defaultRules, ...gitignoreRules];

          // 应用所有规则
          const filteredFiles = applyIgnoreRules(fileArray, allRules);
          resolve(filteredFiles);

        } catch (error) {
          console.error('解析 .gitignore 失败:', error);
          // 解析失败时只使用默认规则
          const filteredFiles = applyIgnoreRules(fileArray, defaultRules);
          resolve(filteredFiles);
        }
      };

      reader.onerror = () => {
        console.error('读取 .gitignore 文件失败');
        // 读取失败时只使用默认规则
        const filteredFiles = applyIgnoreRules(fileArray, defaultRules);
        resolve(filteredFiles);
      };

      reader.readAsText(gitignoreFile);
    });
  };

  // 将文件列表转换为项目结构
  const convertFilesToStructure = (files) => {
    const fileArray = Array.from(files);
    const pathMap = new Map();

    console.log('🏗️ 开始转换文件列表为项目结构，文件数量:', fileArray.length);

    // 获取根文件夹名称
    const rootName = fileArray.length > 0 ?
      fileArray[0].webkitRelativePath.split('/')[0] || 'uploaded-project' :
      'uploaded-project';

    console.log('📁 根文件夹名称:', rootName);

    // 创建根节点
    const root = {
      name: rootName,
      type: 'folder',
      capabilities: '项目根目录',
      children: []
    };

    pathMap.set('', root);

    // 处理每个文件
    fileArray.forEach(file => {
      const relativePath = file.webkitRelativePath || file.name;
      const pathParts = relativePath.split('/').filter(part => part);

      // 移除根文件夹名称（第一个部分）
      if (pathParts.length > 0) {
        pathParts.shift();
      }

      let currentPath = '';
      let currentNode = root;

      // 创建文件夹路径
      for (let i = 0; i < pathParts.length - 1; i++) {
        const folderName = pathParts[i];
        currentPath = currentPath ? `${currentPath}/${folderName}` : folderName;

        if (!pathMap.has(currentPath)) {
          const folderNode = {
            name: folderName,
            type: 'folder',
            capabilities: `${folderName}模块目录`,
            children: []
          };

          currentNode.children.push(folderNode);
          pathMap.set(currentPath, folderNode);
        }

        currentNode = pathMap.get(currentPath);
      }

      // 添加文件
      if (pathParts.length > 0) {
        const fileName = pathParts[pathParts.length - 1];
        const fileNode = {
          name: fileName,
          type: 'file',
          capabilities: `${fileName}文件`
        };

        currentNode.children.push(fileNode);
      }
    });

    console.log('✅ 项目结构转换完成');
    console.log('📊 最终统计:');
    console.log(`  - 根目录名称: ${root.name}`);
    console.log(`  - 根目录子项数: ${root.children.length}`);
    console.log(`  - 路径映射数量: ${pathMap.size}`);

    console.log('📁 根目录直接子项:');
    root.children.forEach((child, index) => {
      console.log(`  ${index + 1}. ${child.name} (${child.type})`);
    });

    return root;
  };

  // 基于从GitHub获取的结构生成项目
  const handleUseGitHubStructure = () => {
    if (repoStructure) {
      // 使用共享计数器为整个树分配唯一ID
      const idCounter = { value: 0 };
      onGenerate(assignIds(repoStructure, 'root', idCounter));
    }
  };

  // 基于上传的文件夹结构生成项目
  const handleUseFolderStructure = () => {
    if (folderStructure) {
      // 使用共享计数器为整个树分配唯一ID
      const idCounter = { value: 0 };
      onGenerate(assignIds(folderStructure, 'root', idCounter));
    }
  };

  // 获取状态文本
  const getStatusText = (status) => {
    switch(status) {
      case 'generating':
        return '正在生成项目结构...';
      case 'parsing':
        return '正在解析JSON结构...';
      case 'complete':
        return '生成完成，正在处理...';
      case 'error':
        return '解析错误，请重试';
      default:
        return '处理中...';
    }
  };
  
  const getBadgeText = (status) => {
    switch(status) {
      case 'generating':
        return '生成中';
      case 'parsing':
        return '解析中';
      case 'complete':
        return '已完成';
      case 'error':
        return '错误';
      default:
        return '处理中';
    }
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setIsLoading(true);
    setError('');
    setStreamingVisible(true);
    setStreamingContent('');
    setStreamProgress(0);
    setStreamStatus('generating');

    try {
      let prompt;

      // 根据生成方式构建不同的提示
      console.log('🔍 生成方式调试信息:');
      console.log('  generationMethod:', generationMethod);
      console.log('  repoStructure存在:', !!repoStructure);
      console.log('  folderStructure存在:', !!folderStructure);
      console.log('  folderStructure内容:', folderStructure ? `${folderStructure.name} (${folderStructure.children?.length || 0} 个子项)` : 'null');

      if (generationMethod === 'repo' && repoStructure) {
        console.log('📋 使用GitHub仓库结构生成');
        prompt = createStructureFromRepoPrompt(
          projectType,
          description,
          repoStructure,
          additionalInfo
        );
      } else if (generationMethod === 'folder' && folderStructure) {
        console.log('📁 使用文件夹结构生成');
        prompt = createStructureFromFolderPrompt(
          projectType,
          description,
          folderStructure,
          additionalInfo
        );
      } else {
        console.log('⚙️ 使用基础结构生成');
        prompt = createProjectStructurePrompt(
          projectType,
          description,
          additionalInfo
        );
      }

      // 使用流式API获取JSON响应
      let treeData;
      try {
        // 使用流式处理，显示实时进度
        treeData = await streamJsonResponse(
          prompt, 
          {
            model: 'LongCat-Large-32K-Chat',
            temperature: 0.2
          },
          (text, progressInfo) => {
            // 格式化显示文本，保留最关键的部分
            let displayText = text;
            
            // 如果文本太长，只显示有意义的部分
            if (text.length > 800) {
              // 查找JSON代码块的开始和结束
              const jsonStart = Math.max(
                text.lastIndexOf("```json"), 
                text.lastIndexOf("{")
              );
              
              if (jsonStart > -1 && jsonStart < text.length - 100) {
                // 取JSON开始部分的前50个字符作为上下文
                const contextStart = Math.max(0, jsonStart - 50);
                // 显示JSON开始部分的上下文和最后300个字符
                displayText = text.substring(contextStart, jsonStart + 100) +
                              "\n...\n" +
                              text.substring(text.length - 300);
              } else {
                // 没有找到JSON块，显示开头和结尾
                displayText = text.substring(0, 200) +
                             "\n...\n" +
                             text.substring(text.length - 400);
              }
            }
            
            setStreamingContent(displayText);
            setStreamProgress(progressInfo.progress);
            setStreamStatus(progressInfo.status);
            
            // 如果已有部分解析的JSON，提前显示预览
            if (progressInfo.json && progressInfo.status === 'parsing') {
              try {
                // 使用共享计数器为整个树分配唯一ID
                const idCounter = { value: 0 };
                const processedTreeData = assignIds(progressInfo.json, 'root', idCounter);
                // 这里不关闭加载状态，只预览
                onGenerate(processedTreeData);
              } catch (e) {
                // 部分解析可能会失败，静默处理
                console.log('部分解析预览失败', e);
              }
            }
          }
        );
      } catch (error) {
        console.error('LLM响应解析错误:', error);
        console.error('错误详情:', {
          message: error.message,
          stack: error.stack,
          prompt: prompt.substring(0, 500) + '...'
        });

        // 提供更详细的错误信息
        let errorMessage = '无法解析大模型返回的结构数据';

        if (error.message.includes('JSON')) {
          errorMessage += '：返回的内容不是有效的JSON格式';
        } else if (error.message.includes('网络') || error.message.includes('fetch')) {
          errorMessage += '：网络请求失败，请检查网络连接';
        } else if (error.message.includes('timeout')) {
          errorMessage += '：请求超时，请稍后重试';
        } else {
          errorMessage += `：${error.message}`;
        }

        errorMessage += '。建议：1) 简化项目描述 2) 减少文件夹复杂度 3) 重试操作';

        throw new Error(errorMessage);
      }

      // 使用共享计数器为整个树分配唯一ID
      const idCounter = { value: 0 };
      const processedTreeData = assignIds(treeData, 'root', idCounter);
      onGenerate(processedTreeData);
      
      // 生成完成，一段时间后关闭窗口
      setTimeout(() => {
        // 设置完成状态
        setStreamStatus('complete');
        setStreamProgress(100);
        
        // 延迟关闭
        setTimeout(() => {
          onClose();
        }, 1500);
      }, 500);
    } catch (err) {
      console.error('生成项目结构错误:', err);
      setError(err.message || '生成项目结构时发生错误，请重试。');
      setStreamStatus('error');
    } finally {
      setIsLoading(false);
    }
  };

  // 获取与项目类型相关的描述文本占位符
  const getDescriptionPlaceholder = (projectType) => {
    switch(projectType) {
      case PROJECT_TYPES.LLM_PYTHON:
        return "请描述您要创建的LLM项目，例如：一个基于知识图谱的检索增强生成(RAG)系统，用于医疗领域问答，包含文档解析、向量存储和LLM调用等功能";
      case PROJECT_TYPES.PYTHON_ALGORITHM:
        return "请描述您要创建的算法项目，例如：图像分类模型、自然语言处理算法、推荐系统、CTR预估、时间序列预测、聚类分析或任何机器学习/深度学习算法";
      default:
        return "请描述您要创建的项目，例如：一个在线电子商务平台，包含用户管理、商品展示和支付功能";
    }
  };

  // 获取与项目类型相关的额外信息占位符
  const getAdditionalInfoPlaceholder = (projectType) => {
    switch(projectType) {
      case PROJECT_TYPES.LLM_PYTHON:
        return "例如：需要支持OpenAI和本地Llama模型，使用Chroma作为向量数据库，包含评估模块";
      case PROJECT_TYPES.PYTHON_ALGORITHM:
        return "例如：使用的算法框架(PyTorch/TensorFlow/Scikit-learn等)，数据处理需求，特征工程方式，评估指标等";
      default:
        return "例如：需要使用特定的框架、库或遵循特定的设计模式";
    }
  };

  return (
    <FloatingWindow>
      <WindowContent>
        <Header>
          <Title>快速创建代码概设(项目架构)</Title>
          <CloseButton onClick={onClose}>
            <FaTimes />
          </CloseButton>
        </Header>
        
        <Form onSubmit={handleSubmit}>
          {/* 生成方式选择器 */}
          <FormGroup>
            <Label>选择生成方式</Label>
            <MethodSelector>
              <MethodTab
                type="button"
                active={generationMethod === 'basic'}
                onClick={() => setGenerationMethod('basic')}
              >
                基础生成
              </MethodTab>
              <MethodTab
                type="button"
                active={generationMethod === 'repo'}
                onClick={() => setGenerationMethod('repo')}
              >
                GitHub仓库
              </MethodTab>
              <MethodTab
                type="button"
                active={generationMethod === 'folder'}
                onClick={() => setGenerationMethod('folder')}
              >
                上传文件夹
              </MethodTab>
            </MethodSelector>
          </FormGroup>

          {/* GitHub仓库方式 */}
          {generationMethod === 'repo' && (
            <FormGroup>
              <Label htmlFor="repoUrl">GitHub仓库链接</Label>
            <InputGroup>
              <LeftInput 
                id="repoUrl"
                value={repoUrl}
                onChange={(e) => setRepoUrl(e.target.value)}
                placeholder="例如：https://github.com/username/repository"
              />
              <Button 
                type="button" 
                onClick={fetchGitHubRepoStructure}
                disabled={repoLoading || !repoUrl.trim()}
              >
                {repoLoading ? <LoadingIndicator /> : <FaGithub />}
                {repoLoading ? '获取中...' : '获取结构'}
              </Button>
            </InputGroup>
            {repoError && <ErrorMessage>{repoError}</ErrorMessage>}
            
            {repoStructurePreview && (
              <>
                <RepoStructure>
                  {repoStructurePreview}
                </RepoStructure>
                <Button 
                  type="button" 
                  onClick={handleUseGitHubStructure}
                  style={{ marginTop: '8px' }}
                >
                  <FaSyncAlt />
                  直接使用此结构
                </Button>
              </>
            )}
            </FormGroup>
          )}

          {/* 文件夹上传方式 */}
          {generationMethod === 'folder' && (
            <FormGroup>
              <Label>上传项目文件夹</Label>
              <input
                type="file"
                webkitdirectory=""
                directory=""
                multiple
                onChange={handleFolderUpload}
                style={{ display: 'none' }}
                id="folderInput"
              />
              <FolderUploadArea
                onClick={() => document.getElementById('folderInput').click()}
                onDrop={handleFolderDrop}
                onDragOver={(e) => e.preventDefault()}
                onDragEnter={(e) => e.preventDefault()}
              >
                <FolderUploadText>
                  点击选择文件夹或拖拽文件夹到此处
                </FolderUploadText>
                <FolderUploadHint>
                  支持整个项目文件夹的上传和分析
                </FolderUploadHint>
              </FolderUploadArea>

              {folderError && <ErrorMessage>{folderError}</ErrorMessage>}

              {folderStructurePreview && (
                <>
                  <FolderStructurePreview>
                    {folderStructurePreview}
                  </FolderStructurePreview>
                  <Button
                    type="button"
                    onClick={handleUseFolderStructure}
                    style={{ marginTop: '8px' }}
                  >
                    <FaSyncAlt />
                    直接使用此结构
                  </Button>
                </>
              )}
            </FormGroup>
          )}

          <FormGroup>
            <Label htmlFor="description">项目描述</Label>
            <TextArea 
              id="description"
              value={description}
              onChange={(e) => setDescription(e.target.value)}
              placeholder={getDescriptionPlaceholder(projectType)}
              required
            />
          </FormGroup>
          
          <FormGroup>
            <Label htmlFor="projectType">项目类型</Label>
            <Select 
              id="projectType"
              value={projectType}
              onChange={(e) => {
                setProjectType(e.target.value);
                // 重置描述，避免不匹配的描述保留
                setDescription('');
              }}
            >
              {Object.entries(PROJECT_TYPE_LABELS).map(([value, label]) => (
                <option key={value} value={value}>
                  {label}
                </option>
              ))}
            </Select>
          </FormGroup>
          
          <FormGroup>
            <Label htmlFor="additionalInfo">额外信息（可选）</Label>
            <Input 
              id="additionalInfo"
              value={additionalInfo}
              onChange={(e) => setAdditionalInfo(e.target.value)}
              placeholder={getAdditionalInfoPlaceholder(projectType)}
            />
          </FormGroup>
          
          {error && <ErrorMessage>{error}</ErrorMessage>}
          
          {/* 流式处理显示区域 */}
          <StreamingContainer visible={streamingVisible}>
            <StreamingHeader>
              <StreamingTitle>
                <FaRobot />
                项目结构生成进度
              </StreamingTitle>
              <StreamingBadge status={streamStatus}>
                {getBadgeText(streamStatus)}
              </StreamingBadge>
            </StreamingHeader>
            <StreamingContent>
              {streamingContent || '正在连接大模型服务...'}
            </StreamingContent>
            <ProgressBar>
              <ProgressIndicator percent={streamProgress} />
            </ProgressBar>
            <GenerationStatus>
              <StatusText>
                {getStatusText(streamStatus)}
              </StatusText>
              <ProgressText>
                {streamProgress}%
              </ProgressText>
            </GenerationStatus>
          </StreamingContainer>
          
          <GenerateButton type="submit" disabled={isLoading || !description.trim()}>
            {isLoading ? <LoadingIndicator /> : <FaRobot />}
            {isLoading ? '生成中...' : '智能生成项目结构'}
          </GenerateButton>
        </Form>
      </WindowContent>
    </FloatingWindow>
  );
};

export default ProjectInitializer; 