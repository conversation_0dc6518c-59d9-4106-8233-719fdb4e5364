/* 提示词分析页面样式 */

.analysis-page {
  width: 100%;
  min-height: 100vh;
  padding: 0;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  display: flex;
  flex-direction: column;
}

/* 顶部标题栏 */
.analysis-header {
  background: white;
  padding: 20px 30px;
  border-bottom: 1px solid #e2e8f0;
  box-shadow: 0 2px 4px rgba(0,0,0,0.05);
}

.analysis-header h1 {
  color: #2c3e50;
  margin: 0 0 20px 0;
  font-size: 24px;
}

/* 左右布局主体 */
.analysis-main {
  display: flex;
  flex: 1;
  min-height: calc(100vh - 120px);
}

/* 左侧边栏 */
.analysis-sidebar {
  width: 300px;
  background: #f8f9fa;
  border-right: 1px solid #e2e8f0;
  padding: 20px;
  overflow-y: auto;
}

.stats-panel {
  background: white;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.stats-panel h3 {
  margin: 0 0 16px 0;
  color: #2d3748;
  font-size: 18px;
  font-weight: 600;
}

.stats-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

/* 右侧内容区域 */
.analysis-content {
  flex: 1;
  padding: 20px 30px;
  overflow-y: auto;
  background: #ffffff;
}

/* 控制面板 */
.control-panel {
  background: #f8f9fa;
  padding: 20px;
  border-radius: 12px;
  margin-bottom: 30px;
  display: flex;
  gap: 20px;
  align-items: center;
  flex-wrap: wrap;
}

.search-section {
  flex: 1;
  min-width: 250px;
}

.search-input {
  width: 100%;
  padding: 12px 16px;
  border: 2px solid #e9ecef;
  border-radius: 8px;
  font-size: 14px;
  transition: border-color 0.3s;
}

.search-input:focus {
  outline: none;
  border-color: #007bff;
}

.filter-section {
  display: flex;
  gap: 10px;
}

.sort-select,
.filter-select {
  padding: 10px 12px;
  border: 2px solid #e9ecef;
  border-radius: 8px;
  background: white;
  font-size: 14px;
  cursor: pointer;
}

.stats-section {
  display: flex;
  gap: 20px;
}

.stat-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  background: #f7fafc;
  border-radius: 6px;
  border-left: 3px solid #4299e1;
}

.stat-label {
  font-size: 14px;
  color: #4a5568;
  font-weight: 500;
}

.stat-value {
  font-size: 16px;
  font-weight: bold;
  color: #2d3748;
}

.stat-value.status-ready {
  color: #38a169;
}

.stat-value.status-fallback {
  color: #d69e2e;
}

.stat-value.status-checking {
  color: #4299e1;
}

.stat-value.status-error {
  color: #e53e3e;
}

/* 评估网格 */
.evaluations-container {
  min-height: 400px;
}

.evaluations-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(500px, 1fr));
  gap: 20px;
}

.evaluation-card {
  background: white;
  border: 2px solid #e9ecef;
  border-radius: 12px;
  padding: 20px;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.evaluation-card:hover {
  border-color: #007bff;
  box-shadow: 0 4px 12px rgba(0,123,255,0.15);
  transform: translateY(-2px);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 15px;
}

.evaluation-meta {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.evaluation-date {
  font-size: 12px;
  color: #6c757d;
}

.improvement-badge {
  color: white;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 11px;
  font-weight: bold;
  text-align: center;
}

.score-summary {
  text-align: right;
}

.score-change {
  display: block;
  font-size: 16px;
  font-weight: bold;
  color: #2c3e50;
  margin-bottom: 4px;
}

.score-improvement {
  color: #28a745;
  font-weight: bold;
  font-size: 14px;
}

/* 提示词对比 */
.prompts-comparison {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 15px;
  margin-bottom: 15px;
}

.prompt-section h4 {
  margin: 0 0 8px 0;
  font-size: 14px;
  color: #495057;
}

.prompt-content {
  background: #f8f9fa;
  padding: 12px;
  border-radius: 8px;
  font-size: 13px;
  line-height: 1.4;
  color: #495057;
  margin-bottom: 8px;
  min-height: 60px;
}

.prompt-score {
  font-size: 12px;
  font-weight: bold;
  color: #007bff;
}

.card-footer {
  text-align: center;
  padding-top: 15px;
  border-top: 1px solid #e9ecef;
}

.view-details-btn {
  background: #007bff;
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 6px;
  font-size: 12px;
  cursor: pointer;
  transition: background-color 0.3s;
}

.view-details-btn:hover {
  background: #0056b3;
}

/* 模态框 */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0,0,0,0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: 20px;
}

.modal-content {
  background: white;
  border-radius: 12px;
  max-width: 1000px;
  width: 100%;
  max-height: 90vh;
  overflow-y: auto;
  box-shadow: 0 10px 30px rgba(0,0,0,0.3);
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 30px;
  border-bottom: 1px solid #e9ecef;
}

.modal-header h2 {
  margin: 0;
  color: #2c3e50;
}

.close-btn {
  background: none;
  border: none;
  font-size: 24px;
  cursor: pointer;
  color: #6c757d;
  padding: 0;
  width: 30px;
  height: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.close-btn:hover {
  color: #495057;
}

.modal-body {
  padding: 30px;
}

/* 详细对比 */
.detailed-comparison {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 30px;
  margin-bottom: 30px;
}

.prompt-detail h3 {
  margin: 0 0 15px 0;
  color: #2c3e50;
  font-size: 18px;
}

.prompt-full-content {
  background: #f8f9fa;
  padding: 20px;
  border-radius: 8px;
  font-size: 14px;
  line-height: 1.6;
  color: #495057;
  margin-bottom: 15px;
  white-space: pre-wrap;
  min-height: 120px;
}

.overall-score {
  font-size: 16px;
  font-weight: bold;
  color: #007bff;
  text-align: center;
  padding: 10px;
  background: #e3f2fd;
  border-radius: 6px;
}

/* 维度分析 */
.dimensions-analysis {
  margin-bottom: 30px;
}

.dimensions-analysis h3 {
  margin: 0 0 20px 0;
  color: #2c3e50;
}

.evaluation-dimensions {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.dimension-row {
  display: flex;
  align-items: center;
  gap: 15px;
}

.dimension-name {
  min-width: 80px;
  font-size: 14px;
  color: #495057;
  font-weight: 500;
}

.dimension-scores {
  display: flex;
  align-items: center;
  gap: 15px;
  flex: 1;
}

.score-bar {
  display: flex;
  align-items: center;
  gap: 8px;
}

.score-label {
  font-size: 12px;
  color: #6c757d;
  min-width: 30px;
}

.score-value {
  color: white;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: bold;
  min-width: 35px;
  text-align: center;
}

.score-diff {
  font-size: 12px;
  color: #28a745;
  font-weight: bold;
  min-width: 40px;
}

/* 改进总结 */
.improvement-summary h3 {
  margin: 0 0 15px 0;
  color: #2c3e50;
}

.improvement-summary p {
  background: #f8f9fa;
  padding: 15px;
  border-radius: 8px;
  line-height: 1.6;
  margin-bottom: 15px;
}

.improvement-stats {
  display: flex;
  gap: 30px;
}

.improvement-stats .stat {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 15px;
  background: #e3f2fd;
  border-radius: 8px;
  flex: 1;
}

.improvement-stats .stat span:first-child {
  font-size: 12px;
  color: #6c757d;
  margin-bottom: 5px;
}

.improvement-stats .stat span:last-child {
  font-size: 16px;
  font-weight: bold;
  color: #007bff;
}

/* 空状态 */
.empty-state {
  text-align: center;
  padding: 60px 20px;
  color: #6c757d;
}

.empty-state p {
  margin: 10px 0;
}

.test-data-actions {
  margin-top: 30px;
}

.generate-test-data-btn {
  background: linear-gradient(135deg, #007bff, #0056b3);
  color: white;
  border: none;
  padding: 12px 24px;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 4px 12px rgba(0,123,255,0.3);
}

.generate-test-data-btn:hover {
  background: linear-gradient(135deg, #0056b3, #004085);
  transform: translateY(-2px);
  box-shadow: 0 6px 16px rgba(0,123,255,0.4);
}

.test-data-note {
  margin-top: 15px;
  font-size: 12px;
  color: #6c757d;
  font-style: italic;
}

.clear-data-btn {
  background: #dc3545;
  color: white;
  border: none;
  padding: 8px 12px;
  border-radius: 6px;
  font-size: 12px;
  cursor: pointer;
  transition: all 0.3s ease;
  margin-left: 10px;
}

.clear-data-btn:hover {
  background: #c82333;
  transform: translateY(-1px);
}

/* 错误状态 */
.error-container {
  max-width: 600px;
  margin: 40px auto;
  padding: 30px;
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0,0,0,0.1);
  border-left: 4px solid #dc3545;
}

.error-container h2 {
  color: #dc3545;
  margin: 0 0 16px 0;
  font-size: 20px;
}

.error-message {
  background: #f8d7da;
  color: #721c24;
  padding: 16px;
  border-radius: 8px;
  margin-bottom: 20px;
  border: 1px solid #f5c6cb;
  line-height: 1.5;
}

.error-actions {
  display: flex;
  gap: 12px;
  margin-bottom: 24px;
}

.error-help {
  border-top: 1px solid #e9ecef;
  padding-top: 20px;
}

.error-help h3 {
  color: #495057;
  margin: 0 0 12px 0;
  font-size: 16px;
}

.error-help ul {
  margin: 0;
  padding-left: 20px;
  color: #6c757d;
}

.error-help li {
  margin-bottom: 8px;
  line-height: 1.4;
}

/* 加载状态 */
.loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid #e9ecef;
  border-top: 4px solid #007bff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 20px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .analysis-main {
    flex-direction: column;
  }

  .analysis-sidebar {
    width: 100%;
    border-right: none;
    border-bottom: 1px solid #e2e8f0;
    padding: 15px;
  }

  .analysis-content {
    padding: 15px;
  }

  .analysis-header {
    padding: 15px 20px;
  }

  .evaluations-grid {
    grid-template-columns: 1fr;
  }

  .prompts-comparison,
  .detailed-comparison {
    grid-template-columns: 1fr;
  }

  .improvement-stats {
    flex-direction: column;
  }

  .modal-content {
    margin: 10px;
    max-height: 95vh;
  }

  .modal-body {
    padding: 20px;
  }

  .evaluation-method-selector {
    flex-direction: column;
    gap: 8px;
  }

  .form-actions {
    flex-direction: column;
  }

  .stats-list {
    gap: 8px;
  }

  .stat-item {
    padding: 10px 12px;
  }
}

/* 评估方式选择器 */
.evaluation-method-selector {
  display: flex;
  gap: 12px;
  margin: 0;
}

.evaluation-method-tab {
  padding: 10px 20px;
  border: 2px solid #e2e8f0;
  border-radius: 6px;
  background-color: #ffffff;
  color: #4a5568;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  gap: 8px;
}

.evaluation-method-tab.active {
  background-color: #4299e1;
  border-color: #4299e1;
  color: #ffffff;
}

.evaluation-method-tab:hover:not(.active) {
  background-color: #f7fafc;
  border-color: #cbd5e0;
}

/* 人工评估表单 */
.manual-evaluation-form {
  background: white;
  border-radius: 8px;
  padding: 24px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
  margin-bottom: 20px;
}

.form-section {
  margin-bottom: 24px;
}

.form-section-title {
  font-size: 18px;
  font-weight: 600;
  color: #2d3748;
  margin-bottom: 16px;
  padding-bottom: 8px;
  border-bottom: 2px solid #e2e8f0;
}

.form-subsection {
  margin-bottom: 20px;
  padding: 16px;
  background-color: #f7fafc;
  border-radius: 6px;
  border-left: 4px solid #4299e1;
}

.form-subsection-title {
  font-size: 16px;
  font-weight: 500;
  color: #2d3748;
  margin-bottom: 12px;
}

.form-field {
  margin-bottom: 16px;
}

.form-field-label {
  display: block;
  font-size: 14px;
  font-weight: 500;
  color: #4a5568;
  margin-bottom: 6px;
}

.form-field-input {
  width: 100%;
  padding: 8px 12px;
  border: 1px solid #e2e8f0;
  border-radius: 4px;
  font-size: 14px;
  transition: border-color 0.2s ease;
}

.form-field-input:focus {
  outline: none;
  border-color: #4299e1;
  box-shadow: 0 0 0 3px rgba(66, 153, 225, 0.1);
}

.form-field-select {
  width: 100%;
  padding: 8px 12px;
  border: 1px solid #e2e8f0;
  border-radius: 4px;
  font-size: 14px;
  background-color: white;
  cursor: pointer;
}

.form-field-textarea {
  width: 100%;
  min-height: 80px;
  padding: 8px 12px;
  border: 1px solid #e2e8f0;
  border-radius: 4px;
  font-size: 14px;
  resize: vertical;
  font-family: inherit;
}

.score-input-group {
  display: flex;
  align-items: center;
  gap: 12px;
}

.score-input {
  width: 80px;
  text-align: center;
}

.score-range {
  font-size: 12px;
  color: #718096;
}

.form-actions {
  display: flex;
  gap: 12px;
  justify-content: flex-end;
  padding-top: 20px;
  border-top: 1px solid #e2e8f0;
}

.btn-primary {
  background-color: #4299e1;
  color: white;
  border: none;
  padding: 10px 20px;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.btn-primary:hover {
  background-color: #3182ce;
}

.btn-secondary {
  background-color: #e2e8f0;
  color: #4a5568;
  border: none;
  padding: 10px 20px;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.btn-secondary:hover {
  background-color: #cbd5e0;
}

/* 新的评估页面样式 */
.section-header {
  margin-bottom: 20px;
}

.section-header h2 {
  margin: 0 0 8px 0;
  color: #2d3748;
}

.section-header p {
  margin: 0;
  color: #718096;
  font-size: 14px;
}

.evaluations-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 20px;
  margin-top: 20px;
}

.evaluation-card {
  background: white;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
  border: 1px solid #e2e8f0;
  transition: box-shadow 0.2s ease;
}

.evaluation-card:hover {
  box-shadow: 0 4px 8px rgba(0,0,0,0.15);
}

.evaluation-header {
  display: flex;
  justify-content: between;
  align-items: flex-start;
  margin-bottom: 12px;
}

.evaluation-header h3,
.evaluation-header h4 {
  margin: 0;
  font-size: 16px;
  color: #2d3748;
  flex: 1;
}

.evaluation-score {
  background-color: #4299e1;
  color: white;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;
}

.evaluation-meta {
  color: #718096;
  font-size: 12px;
  margin-bottom: 12px;
}

.dimensions-preview,
.categories-preview {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 8px;
}

.dimension-item,
.category-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 4px 8px;
  background-color: #f7fafc;
  border-radius: 4px;
  font-size: 12px;
}

.dimension-name,
.category-name {
  color: #4a5568;
}

.dimension-score,
.category-score {
  font-weight: 500;
  color: #2d3748;
}

/* LLM评估八个维度样式 */
.llm-dimensions-section {
  margin-top: 16px;
  padding-top: 16px;
  border-top: 1px solid #e2e8f0;
}

.section-title {
  font-size: 14px;
  font-weight: 600;
  color: #2d3748;
  margin-bottom: 12px;
  display: flex;
  align-items: center;
}

.section-title::before {
  content: "📊";
  margin-right: 6px;
}

.dimensions-comparison {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 16px;
}

.dimensions-column h5 {
  font-size: 12px;
  font-weight: 600;
  color: #4a5568;
  margin-bottom: 8px;
  text-align: center;
  padding: 4px 8px;
  background: #edf2f7;
  border-radius: 4px;
}

.dimensions-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: 4px;
}

/* 人工评估总分样式 */
.manual-scores-section {
  margin-top: 16px;
  padding-top: 16px;
  border-top: 1px solid #e2e8f0;
}

.manual-scores-section .section-title::before {
  content: "👤";
}

.manual-scores-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.manual-scores-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 12px;
}

.manual-score-item {
  display: flex;
  flex-direction: column;
  gap: 8px;
  padding: 12px;
  background: #f0fff4;
  border: 1px solid #9ae6b4;
  border-radius: 6px;
  font-size: 13px;
}

.score-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.score-label {
  color: #2f855a;
  font-weight: 500;
}

.score-value {
  color: #1a202c;
  font-weight: 700;
  font-size: 14px;
}

.score-value.no-score {
  color: #718096;
  font-weight: 500;
  font-style: italic;
}

.btn-add-evaluation {
  background: #38a169;
  color: white;
  border: none;
  border-radius: 4px;
  padding: 6px 12px;
  font-size: 12px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  align-self: flex-start;
}

.btn-add-evaluation:hover {
  background: #2f855a;
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(56, 161, 105, 0.3);
}

.btn-add-evaluation:active {
  transform: translateY(0);
  box-shadow: 0 1px 2px rgba(56, 161, 105, 0.3);
}

/* 单列布局时的响应式调整 */
@media (max-width: 768px) {
  .dimensions-comparison,
  .manual-scores-grid {
    grid-template-columns: 1fr;
    gap: 12px;
  }

  .evaluation-card {
    padding: 16px;
  }
}

.prompts-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
  gap: 20px;
  margin-top: 20px;
}

.prompt-card {
  background: white;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
  border: 1px solid #e2e8f0;
}

.prompt-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 16px;
}

.prompt-header h4 {
  margin: 0;
  font-size: 14px;
  color: #2d3748;
  flex: 1;
  margin-right: 12px;
}

.prompt-date {
  color: #718096;
  font-size: 12px;
  white-space: nowrap;
}

.evaluation-actions {
  margin-top: 16px;
}

.evaluation-status {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.status-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 12px;
  background-color: #f7fafc;
  border-radius: 6px;
}

.status-item span:first-child {
  font-size: 14px;
  color: #4a5568;
  font-weight: 500;
}

.evaluated {
  color: #38a169;
  font-size: 12px;
  font-weight: 500;
}

.btn-evaluate {
  background-color: #4299e1;
  color: white;
  border: none;
  padding: 6px 12px;
  border-radius: 4px;
  font-size: 12px;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.btn-evaluate:hover {
  background-color: #3182ce;
}

.completed-evaluations {
  margin-top: 32px;
  padding-top: 24px;
  border-top: 1px solid #e2e8f0;
}

.completed-evaluations h3 {
  margin: 0 0 16px 0;
  color: #2d3748;
}

/* 数据管理面板 */
.data-management-panel {
  background: white;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
  margin-top: 20px;
}

.data-management-panel h4 {
  margin: 0 0 16px 0;
  color: #2d3748;
  font-size: 16px;
  font-weight: 600;
}

.management-actions {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.btn-small {
  padding: 8px 12px;
  border-radius: 6px;
  font-size: 12px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 6px;
}

.btn-danger {
  background-color: #e53e3e;
  color: white;
}

.btn-danger:hover:not(:disabled) {
  background-color: #c53030;
  transform: translateY(-1px);
}

.btn-danger:disabled {
  background-color: #a0aec0;
  color: #718096;
  cursor: not-allowed;
  transform: none;
}

.btn-small.btn-secondary {
  background-color: #e2e8f0;
  color: #4a5568;
}

.btn-small.btn-secondary:hover:not(:disabled) {
  background-color: #cbd5e0;
  transform: translateY(-1px);
}

.btn-small.btn-secondary:disabled {
  background-color: #f7fafc;
  color: #a0aec0;
  cursor: not-allowed;
  transform: none;
}
