/**
 * ElasticSearch代理服务器
 * 用于处理CORS和SSL证书问题
 */

const express = require('express');
const { createProxyMiddleware } = require('http-proxy-middleware');
const cors = require('cors');
const https = require('https');

const app = express();
const PORT = 9201; // 代理服务器端口

// 创建自定义的HTTPS Agent来忽略SSL证书验证
const httpsAgent = new https.Agent({
  rejectUnauthorized: false // 忽略自签名证书
});

// 启用CORS
app.use(cors({
  origin: 'http://localhost:3000',
  credentials: true
}));

// 创建代理中间件
const esProxy = createProxyMiddleware({
  target: 'http://localhost:9200',
  changeOrigin: true,
  secure: false, // 忽略SSL证书验证
  // auth: 'elastic:opltztnp*MYlDGO_Abjs', // ElasticSearch认证信息 - 暂时禁用
  onProxyReq: (proxyReq, req, res) => {
    console.log(`代理请求: ${req.method} ${req.url}`);
  },
  onProxyRes: (proxyRes, req, res) => {
    console.log(`代理响应: ${proxyRes.statusCode} ${req.url}`);
  },
  onError: (err, req, res) => {
    console.error('代理错误:', err.message);
    res.status(500).json({ error: '代理服务器错误', message: err.message });
  }
});

// 使用代理中间件处理所有请求
app.use('/', esProxy);

// 启动代理服务器
app.listen(PORT, () => {
  console.log(`🚀 ElasticSearch代理服务器启动成功！`);
  console.log(`📡 代理地址: http://localhost:${PORT}`);
  console.log(`🎯 目标地址: http://localhost:9200`);
  console.log(`🔐 认证信息: 无需认证 (开发模式)`);
  console.log(`\n现在您可以通过 http://localhost:${PORT} 访问ElasticSearch`);
});

// 优雅关闭
process.on('SIGINT', () => {
  console.log('\n🛑 正在关闭代理服务器...');
  process.exit(0);
});

module.exports = app;
