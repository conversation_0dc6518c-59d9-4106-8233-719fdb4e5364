import React, { useState, useEffect } from 'react';
import styled from 'styled-components';
import { FaFolder, FaFile, FaTrash, FaPlus, FaEdit, FaFolderPlus, FaFileMedical, FaFolderOpen, FaChevronRight, FaChevronDown } from 'react-icons/fa';
import { RiFileList3Line } from 'react-icons/ri';

const EditorContainer = styled.div`
  flex: 1;
  background-color: #ffffff;
  border-radius: 8px;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  box-shadow: 0 0 10px rgba(0, 0, 0, 0.05);
`;

const EditorHeader = styled.div`
  display: flex;
  gap: 10px;
  padding: 16px;
  border-bottom: 1px solid #e2e8f0;
`;

const ActionButton = styled.button`
  display: flex;
  align-items: center;
  gap: 6px;
  background-color: ${props => props.primary ? '#3182ce' : '#edf2f7'};
  color: ${props => props.primary ? '#fff' : '#4a5568'};
  border: none;
  border-radius: 4px;
  padding: 8px 12px;
  cursor: pointer;
  font-size: 14px;
  transition: background-color 0.2s;

  &:hover {
    background-color: ${props => props.primary ? '#2c5282' : '#e2e8f0'};
  }
`;

const TreeContainer = styled.div`
  flex: 1;
  padding: 16px;
  overflow-y: auto;
`;

const TreeNode = styled.div`
  margin-left: ${props => props.level * 20}px;
  margin-bottom: 4px;
`;

const NodeContent = styled.div`
  display: flex;
  align-items: center;
  padding: 8px;
  border-radius: 4px;
  background-color: ${props => props.selected ? '#ebf8ff' : 'transparent'};
  &:hover {
    background-color: #ebf8ff;
  }
`;

const NodeIcon = styled.div`
  margin-right: 8px;
  color: ${props => props.isFolder ? '#3182ce' : '#718096'};
`;

const NodeName = styled.div`
  flex: 1;
  color: #2d3748;
  font-size: 14px;
  cursor: default;
`;

const NodeCapability = styled.div`
  color: #718096;
  font-size: 12px;
  font-style: italic;
  margin-right: 10px;
`;

const NodeActions = styled.div`
  display: flex;
  gap: 8px;
  opacity: 0;
  ${NodeContent}:hover & {
    opacity: 1;
  }
`;

const ActionIcon = styled.button`
  background: none;
  border: none;
  color: #718096;
  cursor: pointer;
  font-size: 14px;
  padding: 4px;
  &:hover {
    color: #3182ce;
  }
`;

const NameInput = styled.input`
  flex: 1;
  background-color: #edf2f7;
  border: 1px solid #e2e8f0;
  border-radius: 4px;
  color: #2d3748;
  font-size: 14px;
  padding: 6px 8px;
  outline: none;
  &:focus {
    border-color: #3182ce;
  }
`;

const Modal = styled.div`
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.2);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
`;

const ModalContent = styled.div`
  width: 400px;
  background-color: #ffffff;
  border-radius: 8px;
  padding: 24px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
`;

const ModalTitle = styled.h3`
  margin-bottom: 16px;
  color: #2d3748;
`;

const FormGroup = styled.div`
  margin-bottom: 16px;
`;

const Label = styled.label`
  display: block;
  color: #4a5568;
  margin-bottom: 8px;
  font-size: 14px;
`;

const Input = styled.input`
  width: 100%;
  padding: 8px 12px;
  background-color: #f8f9fa;
  border: 1px solid #e2e8f0;
  border-radius: 4px;
  color: #2d3748;
  font-size: 14px;
  outline: none;
  &:focus {
    border-color: #3182ce;
  }
`;

const Select = styled.select`
  width: 100%;
  padding: 8px 12px;
  background-color: #f8f9fa;
  border: 1px solid #e2e8f0;
  border-radius: 4px;
  color: #2d3748;
  font-size: 14px;
  outline: none;
  &:focus {
    border-color: #3182ce;
  }
`;

const ButtonGroup = styled.div`
  display: flex;
  justify-content: flex-end;
  gap: 12px;
`;

const Button = styled.button`
  padding: 8px 16px;
  background-color: ${props => props.primary ? '#3182ce' : 'transparent'};
  color: ${props => props.primary ? '#fff' : '#4a5568'};
  border: ${props => props.primary ? 'none' : '1px solid #e2e8f0'};
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  &:hover {
    background-color: ${props => props.primary ? '#2c5282' : '#edf2f7'};
    color: ${props => props.primary ? '#fff' : '#2d3748'};
  }
`;

const FolderIcon = styled.div`
  display: flex;
  align-items: center;
  margin-right: 8px;
  color: ${props => props.isOpen ? '#3182ce' : '#3182ce'};
  cursor: pointer;
`;

const ExpandIcon = styled.div`
  display: flex;
  align-items: center;
  margin-right: 4px;
  font-size: 12px;
  color: #718096;
  cursor: pointer;
  width: 16px;
  height: 16px;
  &:hover {
    color: #3182ce;
  }
`;

// 验证是否是真正的子节点关系
const isChildOf = (childId, parentId) => {
  // 基本验证
  if (!childId || !parentId) {
    console.log(`isChildOf检查失败: 无效的ID - 子ID: ${childId}, 父ID: ${parentId}`);
    return false;
  }
  
  if (childId === parentId) {
    console.log(`isChildOf检查失败: 节点不能是自己的子节点 - ID: ${childId}`);
    return false;
  }
  
  // 特殊情况检查
  if (parentId === 'root' && !childId.startsWith('root_')) {
    console.log(`isChildOf检查失败: 根节点的子节点ID必须以'root_'开头 - 子ID: ${childId}`);
    return false;
  }
  
  // 将ID拆分为段，检查是否构成真正的父子关系
  const parentParts = parentId.split('_');
  const childParts = childId.split('_');
  
  // 子节点的部分数量必须大于父节点
  if (childParts.length <= parentParts.length) {
    console.log(`isChildOf检查失败: 子ID段数不大于父ID段数 - 子ID: ${childId} (${childParts.length}段), 父ID: ${parentId} (${parentParts.length}段)`);
    return false;
  }
  
  // 检查父节点的所有部分是否与子节点的对应部分完全匹配
  for (let i = 0; i < parentParts.length; i++) {
    if (parentParts[i] !== childParts[i]) {
      console.log(`isChildOf检查失败: ID段不匹配 - 子ID: ${childId}, 父ID: ${parentId}, 在位置 ${i}: ${childParts[i]} != ${parentParts[i]}`);
      return false;
    }
  }
  
  // 所有检查都通过，确认是父子关系
  console.log(`isChildOf检查通过: ${childId} 是 ${parentId} 的子节点`);
  return true;
};

// 检查两个ID是否相同或者是否有父子关系
const isRelated = (id1, id2) => {
  return id1 === id2 || isChildOf(id1, id2) || isChildOf(id2, id1);
};

// 生成新节点ID的函数
const generateNodeId = (parentId, index) => {
  // 确保使用数字索引而不是时间戳，使ID更可预测
  if (index === undefined) {
    console.warn('生成节点ID时缺少索引值，将使用当前时间作为备用');
    index = Date.now();
  }
  
  // 对于根节点的特殊处理
  if (parentId === 'root') {
    return `root_${index}`;
  }
  
  // 为子节点创建一个ID，使用父ID_索引格式
  return `${parentId}_${index}`;
};

// 确保节点ID在树中是唯一的
const ensureUniqueNodeId = (tree, baseId) => {
  // 所有现有节点ID的集合
  const existingIds = new Set();
  
  // 递归收集所有现有ID
  const collectIds = (node) => {
    if (!node) return;
    
    existingIds.add(node.id);
    
    if (node.children && node.children.length > 0) {
      node.children.forEach(collectIds);
    }
  };
  
  collectIds(tree);
  
  // 如果基础ID已存在，生成新的ID
  if (!existingIds.has(baseId)) {
    return baseId; // 基础ID是唯一的，可以直接使用
  }
  
  // 基础ID已存在，尝试添加后缀直到找到唯一ID
  let suffix = 1;
  let newId = `${baseId}_${suffix}`;
  
  while (existingIds.has(newId)) {
    suffix++;
    newId = `${baseId}_${suffix}`;
  }
  
  console.log(`ID冲突: ${baseId} 已存在，使用新ID: ${newId}`);
  return newId;
};

// 修改validateTreeStructure函数增加ID唯一性检测
const validateTreeStructure = (node, parentId = null, idSet = new Set()) => {
  if (!node) return [];
  
  const errors = [];
  
  // 检查节点是否有ID
  if (!node.id) {
    errors.push(`节点缺少ID: ${node.name || 'unnamed'}`);
  } else {
    // 检查ID唯一性
    if (idSet.has(node.id)) {
      errors.push(`ID重复: ${node.id} (${node.name})`);
    } else {
      idSet.add(node.id);
    }
  }
  
  // 如果有父节点，验证ID的父子关系
  if (parentId && node.id) {
    // 当父ID为root时的特殊情况
    if (parentId === 'root') {
      if (!node.id.startsWith('root_')) {
        errors.push(`ID关系错误: 节点${node.id}应该是root的子节点，ID应该以'root_'开头`);
      }
    } else {
      // 检查其他父子关系
      if (!isChildOf(node.id, parentId)) {
        errors.push(`ID关系错误: 节点${node.id}应该是${parentId}的子节点`);
      }
    }
  }
  
  // 递归检查子节点
  if (node.children && node.children.length > 0) {
    node.children.forEach(child => {
      const childErrors = validateTreeStructure(child, node.id, idSet);
      errors.push(...childErrors);
    });
  }
  
  return errors;
};

// 创建用于恢复树结构的函数
const repairTreeStructure = (tree) => {
  console.log('开始修复树结构');
  
  // 用于跟踪已分配的ID，避免重复
  const assignedIds = new Set();
  
  // 递归为每个节点重新分配ID
  const reassignIds = (node, parentId = 'root', index = 0) => {
    if (!node) return null;
    
    // 创建新的节点ID
    let nodeId;
    if (index === 0 && parentId === 'root') {
      nodeId = 'root'; // 根节点
    } else {
      nodeId = `${parentId}_${index}`;
    }
    
    // 检查ID是否已存在，如果存在则生成新的唯一ID
    if (assignedIds.has(nodeId)) {
      let uniqueIndex = index;
      let uniqueId = nodeId;
      
      // 不断增加索引直到找到唯一的ID
      while (assignedIds.has(uniqueId)) {
        uniqueIndex += 1000; // 使用较大步长避免与正常索引冲突
        uniqueId = `${parentId}_${uniqueIndex}`;
      }
      
      console.log(`修复ID冲突: ${nodeId} 已存在，使用新ID: ${uniqueId}`);
      nodeId = uniqueId;
    }
    
    // 记录已分配的ID
    assignedIds.add(nodeId);
    
    // 创建新的节点对象，包含ID
    const newNode = {
      ...node,
      id: nodeId
    };
    
    // 如果有子节点，递归处理
    if (node.children && Array.isArray(node.children)) {
      // 为每个子节点分配连续的索引
      newNode.children = node.children
        .map((child, childIndex) => reassignIds(child, nodeId, childIndex))
        .filter(Boolean); // 过滤掉null
    }
    
    return newNode;
  };
  
  // 从根节点开始递归
  const result = reassignIds(tree);
  console.log('树结构修复完成');
  return result;
};

// 在树中查找特定ID的节点
const findNodeById = (tree, id) => {
  if (!tree) return null;
  
  if (tree.id === id) {
    return tree;
  }
  
  if (tree.children && tree.children.length > 0) {
    for (const child of tree.children) {
      const found = findNodeById(child, id);
      if (found) return found;
    }
  }
  
  return null;
};

// 添加状态指示器样式
const StatusIndicator = styled.div`
  display: flex;
  align-items: center;
  margin-left: 10px;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  background-color: ${props => props.status === 'ok' ? '#48bb78' : '#f56565'};
  color: white;
  cursor: pointer;
`;

const TreeEditor = ({ treeData, updateTree }) => {
  const [selectedNode, setSelectedNode] = useState(null);
  const [editMode, setEditMode] = useState(false);
  const [editName, setEditName] = useState('');
  const [showModal, setShowModal] = useState(false);
  const [modalType, setModalType] = useState(''); // 'add', 'capabilities'
  const [newItemData, setNewItemData] = useState({
    name: '',
    type: 'file',
    capabilities: ''
  });
  const [parentNode, setParentNode] = useState(null);
  const [debugMode, setDebugMode] = useState(false); // 调试模式
  const [treeHealth, setTreeHealth] = useState({ status: 'ok', errors: [] }); // 树结构健康状态
  
  const [expandedNodes, setExpandedNodes] = useState({
    root: true // 默认根节点展开
  });

  // 每次树结构更新时进行验证
  useEffect(() => {
    if (treeData) {
      const validationErrors = validateTreeStructure(treeData);
      
      // 更新树健康状态
      setTreeHealth({
        status: validationErrors.length > 0 ? 'error' : 'ok',
        errors: validationErrors
      });
      
      if (validationErrors.length > 0) {
        console.warn('树结构验证发现错误:', validationErrors);
        // 严重错误时自动修复
        if (validationErrors.some(err => err.includes('ID重复'))) {
          console.error('检测到ID重复，尝试自动修复树结构');
          const fixedTree = repairTreeStructure(treeData);
          updateTree(fixedTree);
        }
      } else {
        console.log('树结构验证通过');
      }
    }
  }, [treeData, updateTree]);

  // 初始化时预设所有文件夹为展开状态
  useEffect(() => {
    if (treeData) {
      const expandedMap = { root: true };
      
      // 递归函数来收集所有文件夹的ID
      const collectFolderIds = (node) => {
        if (node.type === 'folder' && node.id) {
          expandedMap[node.id] = true;
        }
        
        if (node.children && node.children.length > 0) {
          node.children.forEach(collectFolderIds);
        }
      };
      
      collectFolderIds(treeData);
      setExpandedNodes(expandedMap);
    }
  }, [treeData]);
  
  const toggleNodeExpand = (nodeId, e) => {
    if (!nodeId) {
      console.error('尝试展开/折叠无效的节点ID:', nodeId);
      return;
    }

    if (e) e.stopPropagation(); // 阻止事件冒泡
    
    console.log(`切换节点展开状态: ${nodeId}, 当前状态: ${expandedNodes[nodeId] ? '展开' : '折叠'}`);
    
    // 验证ID有效性
    const idExists = findNodeById(treeData, nodeId) !== null;
    if (!idExists) {
      console.error(`无法展开/折叠节点: ID ${nodeId} 在树中不存在`);
      return;
    }

    setExpandedNodes(prev => ({
      ...prev,
      [nodeId]: !prev[nodeId]
    }));
  };
  
  const isNodeExpanded = (nodeId) => {
    return !!expandedNodes[nodeId];
  };

  const handleNodeClick = (node) => {
    setSelectedNode(node);
  };

  const handleEditClick = (node, e) => {
    e.stopPropagation(); // 防止触发节点选择
    setSelectedNode(node);
    setEditMode(true);
    setEditName(node.name);
  };

  const handleNameChange = (e) => {
    setEditName(e.target.value);
  };

  const handleNameSubmit = () => {
    if (editName.trim() !== '') {
      const newTree = updateNodeInTree(treeData, selectedNode.id, { name: editName });
      updateTree(newTree);
      setEditMode(false);
    }
  };

  const handleKeyDown = (e) => {
    if (e.key === 'Enter') {
      handleNameSubmit();
    } else if (e.key === 'Escape') {
      setEditMode(false);
    }
  };

  const handleAddClick = (node, type, e) => {
    if (e) e.stopPropagation();
    
    // 确保节点存在且有效
    if (!node || !node.id) {
      console.error('尝试向无效节点添加子节点');
      return;
    }
    
    // 如果不是文件夹，不允许添加子节点
    if (node.type !== 'folder' && node.id !== 'root') {
      console.error(`无法向非文件夹节点添加子节点: ${node.id} (${node.name})`);
      return;
    }
    
    setParentNode(node);
    setModalType('add');
    setNewItemData({
      name: '',
      type: type || 'file',
      capabilities: ''
    });
    setShowModal(true);
  };

  const handleRemoveClick = (nodeId, e) => {
    if (e) e.stopPropagation();
    
    // 防止删除根节点
    if (nodeId === 'root') {
      console.error('根节点不能被删除');
      return;
    }
    
    // 确保nodeId有效
    if (!nodeId) {
      console.error('尝试删除无效的节点ID');
      return;
    }
    
    // 先记录所有要删除的节点ID（包括子节点）
    const idsToRemove = [];
    
    // 递归收集节点及其所有子节点的ID
    const collectAllChildIds = (node) => {
      if (!node) return;
      
      // 添加当前节点ID
      idsToRemove.push(node.id);
      console.log(`将删除节点: ${node.id} (${node.name})`);
      
      // 递归添加所有子节点ID
      if (node.children && node.children.length > 0) {
        node.children.forEach(child => collectAllChildIds(child));
      }
    };
    
    // 先找到要删除的节点
    const nodeToDelete = findNodeById(treeData, nodeId);
    
    if (nodeToDelete) {
      // 收集这个节点及其所有子节点的ID
      collectAllChildIds(nodeToDelete);
      console.log(`将删除以下节点: ${idsToRemove.join(', ')}`);
      
      // 从树中删除节点
      const newTree = removeNodeFromTree(treeData, nodeId);
      
      // 从expandedNodes中删除所有相关节点
      setExpandedNodes(prev => {
        const newExpandedNodes = { ...prev };
        idsToRemove.forEach(id => {
          if (newExpandedNodes[id]) {
            console.log(`从expandedNodes中移除: ${id}`);
            delete newExpandedNodes[id];
          }
        });
        return newExpandedNodes;
      });
      
      // 更新选中状态
      // 如果当前选中的节点被删除，清除选中状态
      if (selectedNode && idsToRemove.includes(selectedNode.id)) {
        console.log(`选中的节点 ${selectedNode.id} 被删除，清除选中状态`);
        setSelectedNode(null);
        setEditMode(false); // 同时退出编辑模式
      }
      
      // 更新树
      updateTree(newTree);
    } else {
      console.error(`找不到ID为 ${nodeId} 的节点，无法删除`);
    }
  };

  const handleAddCapability = (node, e) => {
    if (e) e.stopPropagation();
    setSelectedNode(node);
    setModalType('capabilities');
    setNewItemData({
      ...newItemData,
      capabilities: node.capabilities || ''
    });
    setShowModal(true);
  };

  const handleModalClose = () => {
    setShowModal(false);
  };

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setNewItemData({ ...newItemData, [name]: value });
  };

  const handleFormSubmit = () => {
    if (modalType === 'add') {
      if (newItemData.name.trim() !== '') {
        // 确保父节点存在
        if (!parentNode) {
          console.error('父节点不存在，无法添加新节点');
          setShowModal(false);
          return;
        }
        
        // 再次确认父节点在树中存在
        const parentStillExists = findNodeById(treeData, parentNode.id);
        if (!parentStillExists) {
          console.error(`父节点 ${parentNode.id} 不再存在于树中`);
          setShowModal(false);
          return;
        }
        
        // 计算子节点的索引 (父节点现有子节点数量)
        const childIndex = parentNode.children ? parentNode.children.length : 0;
        
        // 为新节点生成基础ID
        const baseId = generateNodeId(parentNode.id, childIndex);
        
        // 确保ID在整个树中是唯一的
        const uniqueId = ensureUniqueNodeId(treeData, baseId);
        
        console.log(`为新节点生成ID: ${uniqueId}, 父节点: ${parentNode.id}, 索引: ${childIndex}`);
        
        // 创建新节点对象
        const newNode = {
          id: uniqueId,
          name: newItemData.name,
          type: newItemData.type,
          capabilities: newItemData.capabilities || undefined,
          children: newItemData.type === 'folder' ? [] : undefined
        };
        
        // 如果是文件夹，默认展开
        if (newItemData.type === 'folder') {
          setExpandedNodes(prev => ({
            ...prev,
            [uniqueId]: true
          }));
        }
        
        // 添加到树中
        const newTree = addNodeToTree(treeData, parentNode.id, newNode);
        
        // 验证新生成的树结构
        const validationErrors = validateTreeStructure(newTree);
        if (validationErrors.length > 0) {
          console.error('添加节点后树结构验证失败:', validationErrors);
          // 可以选择继续或者中止操作
        }
        
        updateTree(newTree);
      }
    } else if (modalType === 'capabilities') {
      if (selectedNode) {
        // 确认选中的节点还存在于树中
        const nodeStillExists = findNodeById(treeData, selectedNode.id);
        if (!nodeStillExists) {
          console.error(`选中的节点 ${selectedNode.id} 不再存在于树中`);
          setShowModal(false);
          return;
        }
        
        const newTree = updateNodeInTree(treeData, selectedNode.id, { 
          capabilities: newItemData.capabilities 
        });
        updateTree(newTree);
      } else {
        console.error('没有选中节点，无法更新功能描述');
      }
    }
    
    setShowModal(false);
  };

  // 更新指定ID的节点属性
  const updateNodeInTree = (node, nodeId, newProps) => {
    if (!node) return null;
    
    // 记录当前修改情况，以便调试
    console.log(`尝试更新节点: ${nodeId}, 当前处理节点: ${node.id}`);
    
    // 如果当前节点是要更新的节点
    if (node.id === nodeId) {
      console.log(`找到并更新节点: ${nodeId}`);
      return { ...node, ...newProps };
    }
    
    // 如果不是且有子节点，递归检查子节点
    if (node.children && node.children.length > 0) {
      const updatedChildren = node.children.map(child => {
        // 如果子节点是目标节点
        if (child.id === nodeId) {
          console.log(`在父节点${node.id}的子节点中找到并更新节点: ${nodeId}`);
          return { ...child, ...newProps };
        }
        
        // 如果目标节点可能是当前子节点的后代
        if (isChildOf(nodeId, child.id)) {
          console.log(`节点${nodeId}可能是${child.id}的子节点，继续查找`);
          return updateNodeInTree(child, nodeId, newProps);
        }
        
        // 否则保留当前子节点
        return child;
      });
      
      return {
        ...node,
        children: updatedChildren
      };
    }
    
    // 如果不是且没有子节点，返回原节点
    return node;
  };

  // 从树中删除指定ID的节点
  const removeNodeFromTree = (node, nodeId) => {
    if (!node) return null;
    
    // 记录当前删除情况，以便调试
    console.log(`尝试删除节点: ${nodeId}, 当前处理节点: ${node.id}`);
    
    // 根节点不能被删除，返回原始树
    if (node.id === 'root' && nodeId === 'root') {
      console.error('根节点不能被删除');
      return node;
    }
    
    // 如果当前节点是要删除的节点
    if (node.id === nodeId) {
      console.log(`找到并删除节点: ${nodeId}`);
      return null;
    }
    
    // 查看是否是直接子节点
    if (node.children && node.children.length > 0) {
      // 直接检查当前节点的子节点列表
      const childIndex = node.children.findIndex(child => child.id === nodeId);
      
      if (childIndex !== -1) {
        // 找到了直接子节点，移除它
        console.log(`在节点 ${node.id} 的直接子节点中找到节点 ${nodeId}，移除它`);
        const newChildren = [
          ...node.children.slice(0, childIndex),
          ...node.children.slice(childIndex + 1)
        ];
        return { ...node, children: newChildren };
      }
      
      // 不是直接子节点，递归查找更深层次
      // 创建更明确的标记，表示是否在某个分支找到了要删除的节点
      let nodeRemoved = false;
      const newChildren = node.children.map(child => {
        // 对于每个子节点，判断目标节点是否可能在其子树中
        
        // 检查模式1：直接是目标节点的父节点
        // 获取子节点的直接子节点ID列表
        const grandchildIds = child.children ? child.children.map(gc => gc.id) : [];
        if (grandchildIds.includes(nodeId)) {
          console.log(`节点 ${nodeId} 是 ${child.id} 的直接子节点`);
          // 在这个子节点中递归查找并删除
          const result = removeNodeFromTree(child, nodeId);
          nodeRemoved = true;
          return result;
        }
        
        // 检查模式2：使用isChildOf检查嵌套更深的关系
        if (isChildOf(nodeId, child.id)) {
          console.log(`节点 ${nodeId} 可能在 ${child.id} 的子树中 (isChildOf判断)`);
          // 在这个子节点中递归查找并删除
          const result = removeNodeFromTree(child, nodeId);
          nodeRemoved = true;
          return result;
        }
        
        // 不在这个分支中，保持不变
        return child;
      });
      
      // 过滤掉可能删除后的null值
      const filteredChildren = newChildren.filter(Boolean);
      
      // 如果删除了节点，或者子节点数量变化了，返回更新后的节点
      if (nodeRemoved || filteredChildren.length !== node.children.length) {
        return { ...node, children: filteredChildren };
      }
    }
    
    // 没有找到要删除的节点，返回原节点
    return node;
  };

  // 向指定ID的节点添加子节点
  const addNodeToTree = (node, parentId, newNode) => {
    if (!node) return null;
    
    // 记录当前添加情况，以便调试
    console.log(`尝试添加节点到: ${parentId}, 当前处理节点: ${node.id}, 新节点ID: ${newNode.id}`);
    
    // 特殊情况：添加到根节点但ID不是root
    if (parentId === 'root' && node.id === 'root') {
      console.log(`直接添加到根节点: ${newNode.id}`);
      return {
        ...node,
        children: [...(node.children || []), newNode]
      };
    }
    
    // 如果当前节点是目标父节点
    if (node.id === parentId) {
      console.log(`找到父节点: ${parentId}, 添加新节点: ${newNode.id}`);
      // 确保节点有children数组
      const childrenArray = node.children || [];
      return {
        ...node,
        children: [...childrenArray, newNode]
      };
    }
    
    // 如果当前节点没有子节点，不需要继续搜索
    if (!node.children || node.children.length === 0) {
      return node;
    }
    
    // 处理子节点，查找正确的添加位置
    let nodeAdded = false;
    const updatedChildren = node.children.map(child => {
      // 直接检查ID匹配
      if (child.id === parentId) {
        nodeAdded = true;
        console.log(`在子节点中找到父节点: ${parentId}, 添加新节点: ${newNode.id}`);
        // 确保有children数组
        const childrenArray = child.children || [];
        return {
          ...child,
          children: [...childrenArray, newNode]
        };
      }
      
      // 检查是否是更深层次的祖先节点
      if (isChildOf(parentId, child.id)) {
        nodeAdded = true;
        console.log(`父节点 ${parentId} 可能在 ${child.id} 的子树中，递归查找`);
        return addNodeToTree(child, parentId, newNode);
      }
      
      // 这个分支不包含目标父节点，保持不变
      return child;
    });
    
    // 如果在任何子树中添加了节点，返回更新后的节点
    if (nodeAdded) {
      return {
        ...node,
        children: updatedChildren
      };
    }
    
    // 在整个树中未找到父节点，返回原节点
    console.log(`在整个树中未找到父节点 ${parentId}，无法添加节点 ${newNode.id}`);
    return node;
  };

  const toggleDebugMode = () => {
    setDebugMode(!debugMode);
  };

  // 添加一个树结构修复按钮
  const handleRepairTree = () => {
    const fixedTree = repairTreeStructure(treeData);
    updateTree(fixedTree);
  };

  const renderTree = (node, level = 0) => {
    // 确保节点存在
    if (!node) return null;

    const isFolder = node.type === 'folder';
    const hasChildren = isFolder && node.children && node.children.length > 0;
    const isExpanded = isNodeExpanded(node.id);

    return (
      <React.Fragment key={node.id}>
        <TreeNode level={level}>
          <NodeContent 
            selected={selectedNode && selectedNode.id === node.id} 
            onClick={(e) => {
              e.stopPropagation();
              handleNodeClick(node);
            }}
            data-node-id={node.id} // 添加数据属性方便调试
          >
            {editMode && selectedNode && selectedNode.id === node.id ? (
              <NameInput 
                type="text" 
                value={editName} 
                onChange={handleNameChange} 
                onBlur={handleNameSubmit}
                onKeyDown={handleKeyDown}
                autoFocus
                onClick={(e) => e.stopPropagation()}
              />
            ) : (
              <>
                {isFolder && hasChildren && (
                  <ExpandIcon onClick={(e) => toggleNodeExpand(node.id, e)} data-icon-type="expand">
                    {isExpanded ? <FaChevronDown /> : <FaChevronRight />}
                  </ExpandIcon>
                )}
                {isFolder && !hasChildren && <ExpandIcon />}
                
                <NodeIcon isFolder={isFolder}>
                  {isFolder ? 
                    (isExpanded ? <FaFolderOpen /> : <FaFolder />) : 
                    <FaFile />
                  }
                </NodeIcon>
                <NodeName>
                  {node.name}
                  {debugMode && <span style={{ fontSize: '10px', color: '#999', marginLeft: '4px' }}>[{node.id}]</span>}
                </NodeName>
                {node.capabilities && (
                  <NodeCapability>
                    <RiFileList3Line style={{ marginRight: '4px' }} />
                    {node.capabilities}
                  </NodeCapability>
                )}
                <NodeActions>
                  <ActionIcon 
                    onClick={(e) => handleEditClick(node, e)} 
                    title="编辑名称"
                    data-action="edit"
                  >
                    <FaEdit />
                  </ActionIcon>
                  <ActionIcon 
                    onClick={(e) => handleAddCapability(node, e)} 
                    title="添加功能描述"
                    data-action="add-capability"
                  >
                    <RiFileList3Line />
                  </ActionIcon>
                  {node.type === 'folder' && (
                    <>
                      <ActionIcon 
                        onClick={(e) => handleAddClick(node, 'file', e)} 
                        title="添加文件"
                        data-action="add-file"
                      >
                        <FaFileMedical />
                      </ActionIcon>
                      <ActionIcon 
                        onClick={(e) => handleAddClick(node, 'folder', e)} 
                        title="添加文件夹"
                        data-action="add-folder"
                      >
                        <FaFolderPlus />
                      </ActionIcon>
                    </>
                  )}
                  <ActionIcon 
                    onClick={(e) => handleRemoveClick(node.id, e)} 
                    title="删除"
                    data-action="remove"
                  >
                    <FaTrash />
                  </ActionIcon>
                </NodeActions>
              </>
            )}
          </NodeContent>
        </TreeNode>
        {isFolder && hasChildren && isExpanded && 
          node.children.map(child => renderTree(child, level + 1))
        }
      </React.Fragment>
    );
  };

  return (
    <EditorContainer>
      <EditorHeader>
        <ActionButton primary onClick={() => handleAddClick(treeData, 'folder')}>
          <FaFolderPlus />
          添加文件夹
        </ActionButton>
        <ActionButton onClick={() => handleAddClick(treeData, 'file')}>
          <FaFileMedical />
          添加文件
        </ActionButton>
        {treeHealth.status === 'error' && (
          <StatusIndicator 
            status="error" 
            title={`树结构存在 ${treeHealth.errors.length} 个问题`}
            onClick={handleRepairTree}
          >
            检测到结构问题，点击修复
          </StatusIndicator>
        )}
      </EditorHeader>
      <TreeContainer>
        {renderTree(treeData)}
      </TreeContainer>

      {showModal && (
        <Modal>
          <ModalContent>
            <ModalTitle>
              {modalType === 'add' 
                ? `添加${newItemData.type === 'folder' ? '文件夹' : '文件'}`
                : '编辑功能描述'}
            </ModalTitle>
            
            {modalType === 'add' && (
              <>
                <FormGroup>
                  <Label htmlFor="name">名称</Label>
                  <Input
                    id="name"
                    name="name"
                    value={newItemData.name}
                    onChange={handleInputChange}
                    placeholder={`输入${newItemData.type === 'folder' ? '文件夹' : '文件'}名称`}
                    autoFocus
                  />
                </FormGroup>
                
                <FormGroup>
                  <Label htmlFor="type">类型</Label>
                  <Select
                    id="type"
                    name="type"
                    value={newItemData.type}
                    onChange={handleInputChange}
                  >
                    <option value="file">文件</option>
                    <option value="folder">文件夹</option>
                  </Select>
                </FormGroup>
              </>
            )}
            
            <FormGroup>
              <Label htmlFor="capabilities">功能描述</Label>
              <Input
                id="capabilities"
                name="capabilities"
                value={newItemData.capabilities}
                onChange={handleInputChange}
                placeholder="这个组件的功能是什么？（例如：'API服务'，'数据模型'）"
                autoFocus={modalType === 'capabilities'}
              />
            </FormGroup>
            
            <ButtonGroup>
              <Button onClick={handleModalClose}>取消</Button>
              <Button primary onClick={handleFormSubmit}>
                {modalType === 'add' ? '添加' : '更新'}
              </Button>
            </ButtonGroup>
          </ModalContent>
        </Modal>
      )}
    </EditorContainer>
  );
};

export default TreeEditor; 