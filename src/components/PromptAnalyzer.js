/**
 * 提示词分析页面 - 展示存储的评估数据用于分析
 */

import React, { useState, useEffect } from 'react';
import { localStorageService } from '../services/localStorageService';
import './PromptAnalysisPage.css';

const PromptAnalysisPage = () => {
  const [evaluations, setEvaluations] = useState([]);
  const [loading, setLoading] = useState(true);
  const [selectedEvaluation, setSelectedEvaluation] = useState(null);
  const [sortBy, setSortBy] = useState('timestamp');
  const [filterBy, setFilterBy] = useState('all');
  const [searchQuery, setSearchQuery] = useState('');

  useEffect(() => {
    loadEvaluations();
  }, []);

  /**
   * 加载评估数据
   */
  const loadEvaluations = async () => {
    try {
      setLoading(true);
      console.log('🔍 开始加载评估数据...');

      // 从本地存储获取评估数据
      const data = await localStorageService.exportData();
      console.log('📊 导出的数据:', data);

      const evaluationData = data.evaluations || [];
      console.log('📋 评估数据数量:', evaluationData.length);
      console.log('📋 评估数据详情:', evaluationData);

      // 按时间戳排序
      const sortedEvaluations = evaluationData.sort((a, b) =>
        new Date(b.timestamp) - new Date(a.timestamp)
      );

      setEvaluations(sortedEvaluations);
      console.log('✅ 评估数据加载完成，共', sortedEvaluations.length, '条记录');
    } catch (error) {
      console.error('❌ 加载评估数据失败:', error);
    } finally {
      setLoading(false);
    }
  };

  /**
   * 清除所有数据
   */
  const handleClearAllData = async () => {
    if (window.confirm('确定要清除所有评估数据吗？此操作不可撤销。')) {
      try {
        setLoading(true);
        await localStorageService.clearAllData();
        await loadEvaluations();
      } catch (error) {
        console.error('清除数据失败:', error);
      } finally {
        setLoading(false);
      }
    }
  };

  /**
   * 过滤和排序评估数据
   */
  const getFilteredEvaluations = () => {
    let filtered = evaluations;

    // 搜索过滤
    if (searchQuery) {
      filtered = filtered.filter(evaluation => {
        // 安全地获取提示词内容
        const originalContent = typeof evaluation.originalPrompt === 'string'
          ? evaluation.originalPrompt
          : evaluation.originalPrompt?.content || '';
        const enhancedContent = typeof evaluation.enhancedPrompt === 'string'
          ? evaluation.enhancedPrompt
          : evaluation.enhancedPrompt?.content || '';

        return originalContent.toLowerCase().includes(searchQuery.toLowerCase()) ||
               enhancedContent.toLowerCase().includes(searchQuery.toLowerCase());
      });
    }

    // 改进程度过滤
    if (filterBy !== 'all') {
      filtered = filtered.filter(evaluation => {
        const improvement = evaluation.improvement.absoluteImprovement;
        switch (filterBy) {
          case 'high': return improvement >= 10;
          case 'medium': return improvement >= 5 && improvement < 10;
          case 'low': return improvement < 5;
          default: return true;
        }
      });
    }

    // 排序
    filtered.sort((a, b) => {
      switch (sortBy) {
        case 'improvement':
          return b.improvement.absoluteImprovement - a.improvement.absoluteImprovement;
        case 'originalScore':
          return b.originalPrompt.score - a.originalPrompt.score;
        case 'enhancedScore':
          return b.enhancedPrompt.score - a.enhancedPrompt.score;
        case 'timestamp':
        default:
          return new Date(b.timestamp) - new Date(a.timestamp);
      }
    });

    return filtered;
  };

  /**
   * 获取评估维度的颜色
   */
  const getScoreColor = (score) => {
    if (score >= 90) return '#4caf50'; // 绿色
    if (score >= 80) return '#8bc34a'; // 浅绿
    if (score >= 70) return '#ffc107'; // 黄色
    if (score >= 60) return '#ff9800'; // 橙色
    return '#f44336'; // 红色
  };

  /**
   * 获取改进程度标签
   */
  const getImprovementLabel = (improvement) => {
    if (improvement >= 15) return { label: '显著改进', color: '#4caf50' };
    if (improvement >= 10) return { label: '明显改进', color: '#8bc34a' };
    if (improvement >= 5) return { label: '轻微改进', color: '#ffc107' };
    if (improvement > 0) return { label: '微小改进', color: '#ff9800' };
    return { label: '无改进', color: '#f44336' };
  };

  /**
   * 渲染评估维度
   */
  const renderEvaluationDimensions = (evaluation) => {
    const dimensions = ['specificity', 'clarity', 'structure', 'completeness', 'roleDefinition', 'outputFormat', 'constraints', 'actionability'];
    const dimensionNames = {
      specificity: '具体性',
      clarity: '清晰度',
      structure: '结构性',
      completeness: '完整性',
      roleDefinition: '角色定义',
      outputFormat: '输出格式',
      constraints: '约束条件',
      actionability: '可执行性'
    };

    return (
      <div className="evaluation-dimensions">
        {dimensions.map(dim => (
          <div key={dim} className="dimension-row">
            <span className="dimension-name">{dimensionNames[dim]}</span>
            <div className="dimension-scores">
              <div className="score-bar">
                <span className="score-label">原始</span>
                <div
                  className="score-value"
                  style={{
                    backgroundColor: getScoreColor(
                      evaluation.originalPrompt?.evaluation?.llmEvaluation?.[dim] || 0
                    )
                  }}
                >
                  {evaluation.originalPrompt?.evaluation?.llmEvaluation?.[dim] || 'N/A'}
                </div>
              </div>
              <div className="score-bar">
                <span className="score-label">优化</span>
                <div
                  className="score-value"
                  style={{
                    backgroundColor: getScoreColor(
                      evaluation.enhancedPrompt?.evaluation?.llmEvaluation?.[dim] || 0
                    )
                  }}
                >
                  {evaluation.enhancedPrompt?.evaluation?.llmEvaluation?.[dim] || 'N/A'}
                </div>
              </div>
              <div className="score-diff">
                +{(evaluation.enhancedPrompt.evaluation.llmEvaluation[dim] - evaluation.originalPrompt.evaluation.llmEvaluation[dim]).toFixed(1)}
              </div>
            </div>
          </div>
        ))}
      </div>
    );
  };

  const filteredEvaluations = getFilteredEvaluations();

  if (loading) {
    return (
      <div className="analysis-page">
        <div className="loading">
          <div className="loading-spinner"></div>
          <p>加载评估数据中...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="analysis-page">
      <div className="page-header">
        <h1>📊 提示词分析中心</h1>
        <p>分析存储的提示词评估数据，提取高质量prompt的要点</p>
      </div>

      {/* 控制面板 */}
      <div className="control-panel">
        <div className="search-section">
          <input
            type="text"
            placeholder="搜索提示词内容..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="search-input"
          />
        </div>

        <div className="filter-section">
          <select 
            value={sortBy} 
            onChange={(e) => setSortBy(e.target.value)}
            className="sort-select"
          >
            <option value="timestamp">按时间排序</option>
            <option value="improvement">按改进程度排序</option>
            <option value="originalScore">按原始分数排序</option>
            <option value="enhancedScore">按优化分数排序</option>
          </select>

          <select 
            value={filterBy} 
            onChange={(e) => setFilterBy(e.target.value)}
            className="filter-select"
          >
            <option value="all">所有改进程度</option>
            <option value="high">高改进 (≥10分)</option>
            <option value="medium">中等改进 (5-10分)</option>
            <option value="low">低改进 (&lt;5分)</option>
          </select>
        </div>

        <div className="stats-section">
          <div className="stat-item">
            <span className="stat-label">总评估数</span>
            <span className="stat-value">{evaluations.length}</span>
          </div>
          <div className="stat-item">
            <span className="stat-label">筛选结果</span>
            <span className="stat-value">{filteredEvaluations.length}</span>
          </div>
          {evaluations.length > 0 && (
            <button
              className="clear-data-btn"
              onClick={handleClearAllData}
              title="清除所有数据"
            >
              🗑️ 清除数据
            </button>
          )}
        </div>
      </div>

      {/* 评估列表 */}
      <div className="evaluations-container">
        {filteredEvaluations.length === 0 ? (
          <div className="empty-state">
            <p>暂无评估数据</p>
            <p>请先使用提示词评估功能创建一些数据</p>
          </div>
        ) : (
          <div className="evaluations-grid">
            {filteredEvaluations.map((evaluation, index) => {
              const improvementInfo = getImprovementLabel(evaluation.improvement.absoluteImprovement);
              
              return (
                <div 
                  key={evaluation.id || index} 
                  className="evaluation-card"
                  onClick={() => setSelectedEvaluation(evaluation)}
                >
                  <div className="card-header">
                    <div className="evaluation-meta">
                      <span className="evaluation-date">
                        {new Date(evaluation.timestamp).toLocaleDateString('zh-CN')}
                      </span>
                      <div 
                        className="improvement-badge"
                        style={{ backgroundColor: improvementInfo.color }}
                      >
                        {improvementInfo.label}
                      </div>
                    </div>
                    <div className="score-summary">
                      <span className="score-change">
                        {(() => {
                          const originalScore = typeof evaluation.originalPrompt === 'object'
                            ? evaluation.originalPrompt?.score?.toFixed(1) || 'N/A'
                            : 'N/A';
                          const enhancedScore = typeof evaluation.enhancedPrompt === 'object'
                            ? evaluation.enhancedPrompt?.score?.toFixed(1) || 'N/A'
                            : 'N/A';
                          return `${originalScore} → ${enhancedScore}`;
                        })()}
                      </span>
                      <span className="score-improvement">
                        +{evaluation.improvement?.absoluteImprovement?.toFixed(1) || 'N/A'}
                      </span>
                    </div>
                  </div>

                  <div className="prompts-comparison">
                    <div className="prompt-section">
                      <h4>原始提示词</h4>
                      <div className="prompt-content">
                        {(() => {
                          const originalContent = typeof evaluation.originalPrompt === 'string'
                            ? evaluation.originalPrompt
                            : evaluation.originalPrompt?.content || '';
                          return originalContent.substring(0, 150) + (originalContent.length > 150 ? '...' : '');
                        })()}
                      </div>
                      <div className="prompt-score">
                        评分: {typeof evaluation.originalPrompt === 'object'
                          ? evaluation.originalPrompt?.score?.toFixed(1) || 'N/A'
                          : 'N/A'}
                      </div>
                    </div>

                    <div className="prompt-section">
                      <h4>优化提示词</h4>
                      <div className="prompt-content">
                        {(() => {
                          const enhancedContent = typeof evaluation.enhancedPrompt === 'string'
                            ? evaluation.enhancedPrompt
                            : evaluation.enhancedPrompt?.content || '';
                          return enhancedContent.substring(0, 150) + (enhancedContent.length > 150 ? '...' : '');
                        })()}
                      </div>
                      <div className="prompt-score">
                        评分: {typeof evaluation.enhancedPrompt === 'object'
                          ? evaluation.enhancedPrompt?.score?.toFixed(1) || 'N/A'
                          : 'N/A'}
                      </div>
                    </div>
                  </div>

                  <div className="card-footer">
                    <button className="view-details-btn">
                      查看详细分析 →
                    </button>
                  </div>
                </div>
              );
            })}
          </div>
        )}
      </div>

      {/* 详细分析模态框 */}
      {selectedEvaluation && (
        <div className="modal-overlay" onClick={() => setSelectedEvaluation(null)}>
          <div className="modal-content" onClick={(e) => e.stopPropagation()}>
            <div className="modal-header">
              <h2>详细评估分析</h2>
              <button 
                className="close-btn"
                onClick={() => setSelectedEvaluation(null)}
              >
                ×
              </button>
            </div>

            <div className="modal-body">
              <div className="detailed-comparison">
                <div className="prompt-detail">
                  <h3>原始提示词</h3>
                  <div className="prompt-full-content">
                    {typeof selectedEvaluation.originalPrompt === 'string'
                      ? selectedEvaluation.originalPrompt
                      : selectedEvaluation.originalPrompt?.content || ''}
                  </div>
                  <div className="overall-score">
                    总分: {typeof selectedEvaluation.originalPrompt === 'object'
                      ? selectedEvaluation.originalPrompt?.score?.toFixed(1) || 'N/A'
                      : 'N/A'}
                  </div>
                </div>

                <div className="prompt-detail">
                  <h3>优化提示词</h3>
                  <div className="prompt-full-content">
                    {typeof selectedEvaluation.enhancedPrompt === 'string'
                      ? selectedEvaluation.enhancedPrompt
                      : selectedEvaluation.enhancedPrompt?.content || ''}
                  </div>
                  <div className="overall-score">
                    总分: {typeof selectedEvaluation.enhancedPrompt === 'object'
                      ? selectedEvaluation.enhancedPrompt?.score?.toFixed(1) || 'N/A'
                      : 'N/A'}
                  </div>
                </div>
              </div>

              <div className="dimensions-analysis">
                <h3>各维度评分对比</h3>
                {renderEvaluationDimensions(selectedEvaluation)}
              </div>

              <div className="improvement-summary">
                <h3>改进总结</h3>
                <p>{selectedEvaluation.improvement.summary}</p>
                <div className="improvement-stats">
                  <div className="stat">
                    <span>绝对改进:</span>
                    <span>+{selectedEvaluation.improvement.absoluteImprovement.toFixed(1)}分</span>
                  </div>
                  <div className="stat">
                    <span>相对改进:</span>
                    <span>+{selectedEvaluation.improvement.relativeImprovement.toFixed(1)}%</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default PromptAnalysisPage;
