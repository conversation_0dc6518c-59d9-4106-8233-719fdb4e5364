/**
 * 人工评估表单组件
 */

import React, { useState } from 'react';
import './PromptAnalysisPage.css';

const ManualEvaluationForm = ({ prompt, promptType, workflowId, onSubmit, onCancel }) => {
  const [formData, setFormData] = useState({
    // 效率（一级）
    efficiency: {
      timeConsumption: '', // 耗时（二级）
      interactionRounds: '', // 交互轮次（二级）
      manualIntervention: '' // 人工介入轮次（二级）
    },
    // 完成度（一级）
    completeness: {
      adherence: '', // 贴合度（二级）
      compliance: '', // 服从性（二级）
      creativity: '' // 创造性（二级）
    },
    // 功能性（一级）
    functionality: {
      basicUsability: '', // 基本可用性（二级）
      functionalCorrectness: '' // 功能正确性（二级）
    },
    // 可维护性（一级）
    maintainability: {
      codingStandards: '', // 编码规范性（二级）
      codeComplexity: '', // 代码复杂度（二级）
      codeReadability: '', // 代码可读性（二级）
      codeExtensibility: '', // 代码可扩展性（二级）
      modularity: '' // 模块化程度（二级）
    },
    // 安全性（一级）
    security: {
      securityVulnerabilities: '', // 安全漏洞（二级）
      boundaryReliability: '' // 边界可靠性（二级）
    },
    // 兼容性（一级）
    compatibility: {
      backwardCompatibility: '', // 向后兼容性（二级）
      forwardCompatibility: '' // 向前兼容性（二级）
    },
    // 稳定性（一级）
    stability: {
      performance: '', // 性能（二级）
      runtimeStability: '' // 运行时稳定性（二级）
    }
  });

  const [comments, setComments] = useState('');

  // 评分选项（1-10分）
  const scoreOptions = Array.from({ length: 10 }, (_, i) => i + 1);

  const handleScoreChange = (category, subcategory, value) => {
    setFormData(prev => ({
      ...prev,
      [category]: {
        ...prev[category],
        [subcategory]: parseInt(value) || ''
      }
    }));
  };

  const calculateCategoryScore = (categoryData) => {
    const scores = Object.values(categoryData).filter(score => score !== '');
    if (scores.length === 0) return 0;
    return scores.reduce((sum, score) => sum + score, 0) / scores.length;
  };

  const calculateTotalScore = () => {
    const categories = Object.keys(formData);
    const categoryScores = categories.map(category => calculateCategoryScore(formData[category]));
    const validScores = categoryScores.filter(score => score > 0);
    if (validScores.length === 0) return 0;
    return validScores.reduce((sum, score) => sum + score, 0) / validScores.length;
  };

  const handleSubmit = (e) => {
    e.preventDefault();
    
    // 验证必填字段
    const hasEmptyFields = Object.values(formData).some(category =>
      Object.values(category).some(score => score === '')
    );
    
    if (hasEmptyFields) {
      alert('请填写所有评分项');
      return;
    }

    const evaluationResult = {
      id: `manual_${Date.now()}`,
      workflowId: workflowId,
      promptType,
      prompt,
      timestamp: new Date().toISOString(),
      evaluationType: 'manual',
      scores: formData,
      categoryScores: {
        efficiency: calculateCategoryScore(formData.efficiency),
        completeness: calculateCategoryScore(formData.completeness),
        functionality: calculateCategoryScore(formData.functionality),
        maintainability: calculateCategoryScore(formData.maintainability),
        security: calculateCategoryScore(formData.security),
        compatibility: calculateCategoryScore(formData.compatibility),
        stability: calculateCategoryScore(formData.stability)
      },
      totalScore: calculateTotalScore(),
      normalizedScore: (calculateTotalScore() / 10) * 100,
      comments
    };

    onSubmit(evaluationResult);
  };

  const renderScoreField = (category, subcategory, label, description) => (
    <div className="form-field" key={`${category}-${subcategory}`}>
      <label className="form-field-label">
        {label}
        <span className="score-range">（1-10分）</span>
      </label>
      <div className="score-input-group">
        <select
          className="form-field-select score-input"
          value={formData[category][subcategory]}
          onChange={(e) => handleScoreChange(category, subcategory, e.target.value)}
          required
        >
          <option value="">选择分数</option>
          {scoreOptions.map(score => (
            <option key={score} value={score}>{score}</option>
          ))}
        </select>
        <span className="score-range">{description}</span>
      </div>
    </div>
  );

  return (
    <div className="manual-evaluation-form">
      <h3>人工评估表单 - {promptType === 'original' ? '原始提示词' : '优化提示词'}</h3>
      
      <form onSubmit={handleSubmit}>
        {/* 效率（一级） */}
        <div className="form-section">
          <h4 className="form-section-title">效率</h4>
          <div className="form-subsection">
            <h5 className="form-subsection-title">效率评估</h5>
            {renderScoreField('efficiency', 'timeConsumption', '耗时', '从编辑prompt到最终生成完整代码所需的总时间')}
            {renderScoreField('efficiency', 'interactionRounds', '交互轮次', '与LLM之间的交互次数，包括提问、修改指令、反馈等')}
            {renderScoreField('efficiency', 'manualIntervention', '人工介入轮次', '需要人工介入进行代码修改或完善的次数')}
          </div>
        </div>

        {/* 完成度（一级） */}
        <div className="form-section">
          <h4 className="form-section-title">完成度</h4>
          <div className="form-subsection">
            <h5 className="form-subsection-title">完成度评估</h5>
            {renderScoreField('completeness', 'adherence', '贴合度', '衡量代码对指定功能点的实现程度')}
            {renderScoreField('completeness', 'compliance', '服从性', '衡量采用的技术框架是否符合要求')}
            {renderScoreField('completeness', 'creativity', '创造性', '衡量代码对隐藏功能/优化点的实现程度')}
          </div>
        </div>

        {/* 功能性（一级） */}
        <div className="form-section">
          <h4 className="form-section-title">功能性</h4>
          <div className="form-subsection">
            <h5 className="form-subsection-title">功能性评估</h5>
            {renderScoreField('functionality', 'basicUsability', '基本可用性', '评估代码能否通过编译和基础运行')}
            {renderScoreField('functionality', 'functionalCorrectness', '功能正确性', '检验代码的逻辑是否符合预期，代码的行为是否符合设计意图')}
          </div>
        </div>

        {/* 可维护性（一级） */}
        <div className="form-section">
          <h4 className="form-section-title">可维护性</h4>
          <div className="form-subsection">
            <h5 className="form-subsection-title">可维护性评估</h5>
            {renderScoreField('maintainability', 'codingStandards', '编码规范性', '编码是否遵循《阿里巴巴编码规范》、美团内部组件接入规范、组内自定义规范等')}
            {renderScoreField('maintainability', 'codeComplexity', '代码复杂度', '代码的复杂度是否合理，包括循环复杂度、坏味道、文件数量等')}
            {renderScoreField('maintainability', 'codeReadability', '代码可读性', '代码是否易于阅读和理解，包括注释质量、代码结构等')}
            {renderScoreField('maintainability', 'codeExtensibility', '代码可扩展性', '评估代码在不修改现有核心逻辑的前提下，添加新功能或进行功能扩展的难易程度')}
            {renderScoreField('maintainability', 'modularity', '模块化程度', '衡量代码是否按照功能或业务逻辑划分成了清晰的模块且保持简洁的设计')}
          </div>
        </div>

        {/* 安全性（一级） */}
        <div className="form-section">
          <h4 className="form-section-title">安全性</h4>
          <div className="form-subsection">
            <h5 className="form-subsection-title">安全性评估</h5>
            {renderScoreField('security', 'securityVulnerabilities', '安全漏洞', '新增代码引入的安全风险')}
            {renderScoreField('security', 'boundaryReliability', '边界可靠性', '统计代码中对可能出现的异常情况进行处理的比例')}
          </div>
        </div>

        {/* 兼容性（一级） */}
        <div className="form-section">
          <h4 className="form-section-title">兼容性</h4>
          <div className="form-subsection">
            <h5 className="form-subsection-title">兼容性评估</h5>
            {renderScoreField('compatibility', 'backwardCompatibility', '向后兼容性', '新旧代码的协同工作能力')}
            {renderScoreField('compatibility', 'forwardCompatibility', '向前兼容性', '支持未来扩展的能力')}
          </div>
        </div>

        {/* 稳定性（一级） */}
        <div className="form-section">
          <h4 className="form-section-title">稳定性</h4>
          <div className="form-subsection">
            <h5 className="form-subsection-title">稳定性评估</h5>
            {renderScoreField('stability', 'performance', '性能', '对系统吞吐和延迟的影响')}
            {renderScoreField('stability', 'runtimeStability', '运行时稳定性', '通过长时间运行代码，记录代码崩溃或出现错误的频率')}
          </div>
        </div>

        {/* 评估备注 */}
        <div className="form-section">
          <h4 className="form-section-title">评估备注</h4>
          <div className="form-field">
            <label className="form-field-label">补充说明（可选）</label>
            <textarea
              className="form-field-textarea"
              value={comments}
              onChange={(e) => setComments(e.target.value)}
              placeholder="请输入对本次评估的补充说明..."
            />
          </div>
        </div>

        {/* 评估总分预览 */}
        <div className="form-section">
          <h4 className="form-section-title">评估总分预览</h4>
          <div style={{ padding: '16px', backgroundColor: '#f0f8ff', borderRadius: '6px' }}>
            <p><strong>当前总分：{calculateTotalScore().toFixed(2)} / 10</strong></p>
            <p><strong>标准化分数：{((calculateTotalScore() / 10) * 100).toFixed(1)}%</strong></p>
          </div>
        </div>

        <div className="form-actions">
          <button type="button" className="btn-secondary" onClick={onCancel}>
            取消
          </button>
          <button type="submit" className="btn-primary">
            提交评估
          </button>
        </div>
      </form>
    </div>
  );
};

export default ManualEvaluationForm;
