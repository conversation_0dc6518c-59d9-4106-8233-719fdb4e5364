import React, { useState, useEffect } from 'react';
import styled, { ThemeProvider } from 'styled-components';
import TreeEditor from './components/TreeEditor';
import TreeOutput from './components/TreeOutput';
import Header from './components/Header';
import Footer from './components/Footer';
import ProjectInitializer from './components/ProjectInitializer';
import Sidebar from './components/Sidebar';
import PromptOptimizer from './components/PromptOptimizer';
import PromptAnalysisPage from './components/PromptAnalysisPage';

// Define light and dark themes
const lightTheme = {
  background: '#f8f9fa',
  surface: '#ffffff',
  primary: '#3182ce',
  primaryDark: '#2c5282',
  primaryLight: '#ebf8ff',
  text: '#2d3748',
  textSecondary: '#4a5568',
  textMuted: '#718096',
  border: '#e2e8f0',
  borderLight: '#edf2f7',
  success: '#38a169',
  error: '#e53e3e',
  warning: '#d69e2e'
};

const darkTheme = {
  background: '#1a202c',
  surface: '#2d3748',
  primary: '#63b3ed',
  primaryDark: '#2b6cb0',
  primaryLight: '#2a4365',
  text: '#f7fafc',
  textSecondary: '#e2e8f0',
  textMuted: '#a0aec0',
  border: '#4a5568',
  borderLight: '#2d3748',
  success: '#68d391',
  error: '#fc8181',
  warning: '#f6e05e'
};

const AppContainer = styled.div`
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  width: 100%;
  max-width: 100%;
  margin: 0 auto;
  padding: 0;
  background-color: ${props => props.theme.background};
  color: ${props => props.theme.text};
  transition: all 0.3s ease;
  position: relative;
`;

const HeaderWrapper = styled.div`
  padding: 0 1.5rem;
  position: sticky;
  top: 0;
  z-index: 10;
  background-color: ${props => props.theme.background};
  backdrop-filter: blur(10px);
  box-shadow: 0 1px 5px rgba(0, 0, 0, 0.05);
`;

const MainContent = styled.main`
  display: flex;
  flex: 1;
  overflow: hidden;
  box-shadow: inset 0 2px 5px rgba(0, 0, 0, 0.03);
`;

const ContentArea = styled.div`
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  padding: 0;
  animation: fadeIn 0.3s ease-in-out;
  
  @keyframes fadeIn {
    from {
      opacity: 0;
      transform: translateY(10px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }
`;

const FooterWrapper = styled.div`
  padding: 0 1.5rem;
  border-top: 1px solid ${props => props.theme.borderLight};
  box-shadow: 0 -1px 5px rgba(0, 0, 0, 0.05);
`;

// 规范化树结构，确保所有节点都有正确格式的ID
const normalizeTreeStructure = (treeData) => {
  console.log('开始规范化树结构:', JSON.stringify(treeData, null, 2));
  
  // 用于跟踪已分配的ID，避免重复
  const assignedIds = new Set();
  
  // 递归为每个节点分配ID
  const assignIds = (node, parentId = 'root', index = 0) => {
    if (!node) return null;
    
    // 创建新的节点ID
    let nodeId;
    if (index === 0 && parentId === 'root') {
      nodeId = 'root'; // 根节点
    } else {
      nodeId = `${parentId}_${index}`;
    }
    
    // 检查ID是否已存在，如果存在则生成新的唯一ID
    if (assignedIds.has(nodeId)) {
      let uniqueIndex = index;
      let uniqueId = nodeId;
      
      // 不断增加索引直到找到唯一的ID
      while (assignedIds.has(uniqueId)) {
        uniqueIndex += 1000; // 使用较大步长避免与正常索引冲突
        uniqueId = `${parentId}_${uniqueIndex}`;
      }
      
      console.log(`ID冲突: ${nodeId} 已存在，使用新ID: ${uniqueId}`);
      nodeId = uniqueId;
    }
    
    // 记录已分配的ID
    assignedIds.add(nodeId);
    
    console.log(`分配ID: ${nodeId}, 原始ID: ${node.id || '无'}, 父ID: ${parentId}, 索引: ${index}`);
    
    // 创建新的节点对象，包含ID
    const newNode = {
      ...node,
      id: nodeId
    };
    
    // 如果有子节点，递归处理
    if (node.children && Array.isArray(node.children)) {
      // 为每个子节点分配连续的索引
      newNode.children = node.children
        .map((child, childIndex) => assignIds(child, nodeId, childIndex))
        .filter(Boolean); // 过滤掉null
    }
    
    return newNode;
  };
  
  // 从根节点开始递归
  const result = assignIds(treeData);
  console.log('规范化完成:', JSON.stringify(result, null, 2));
  return result;
};

const App = () => {
  // Add theme state
  const [darkMode, setDarkMode] = useState(false);
  
  // State to track active module
  const [activeModule, setActiveModule] = useState('new-project');
  
  // State to track if the initializer should be shown
  const [showInitializer, setShowInitializer] = useState(true);
  
  // State to track project type
  const [projectType, setProjectType] = useState(null);
  
  // Tree structure state
  const [treeData, setTreeData] = useState({
    id: 'root',
    name: '项目',
    type: 'folder',
    children: [
      {
        id: 'root_0',
        name: 'src',
        type: 'folder',
        children: [
          {
            id: 'root_0_0',
            name: 'components',
            type: 'folder',
            children: []
          },
          {
            id: 'root_0_1',
            name: 'styles',
            type: 'folder',
            children: []
          },
          {
            id: 'root_0_2',
            name: 'App.js',
            type: 'file',
            capabilities: 'React组件'
          },
          {
            id: 'root_0_3',
            name: 'index.js',
            type: 'file',
            capabilities: '入口文件'
          }
        ]
      },
      {
        id: 'root_1',
        name: 'public',
        type: 'folder',
        children: [
          {
            id: 'root_1_0',
            name: 'index.html',
            type: 'file',
            capabilities: 'HTML模板'
          }
        ]
      },
      {
        id: 'root_2',
        name: 'package.json',
        type: 'file',
        capabilities: '项目配置'
      },
      {
        id: 'root_3',
        name: 'README.md',
        type: 'file',
        capabilities: '项目文档'
      }
    ]
  });

  // Toggle dark mode function
  const toggleTheme = () => {
    setDarkMode(!darkMode);
  };

  // Function to handle initializer close
  const handleCloseInitializer = () => {
    setShowInitializer(false);
  };

  // Function to handle the tree data generated from the initializer
  const handleGeneratedTree = (generatedTreeData, selectedProjectType = null) => {
    // 在设置树数据之前确保ID结构一致
    const normalizedTree = normalizeTreeStructure(generatedTreeData);
    console.log('规范化后的树结构:', normalizedTree);
    console.log('项目类型:', selectedProjectType);
    setTreeData(normalizedTree);
    setProjectType(selectedProjectType);
    setShowInitializer(false);
  };

  // Function to update the tree data (used by TreeEditor)
  const updateTree = (newTreeData) => {
    setTreeData(newTreeData);
  };

  // Function to show the initializer again (e.g., when requested from button)
  const showProjectInitializer = () => {
    setActiveModule('new-project');
    setShowInitializer(true);
  };

  return (
    <ThemeProvider theme={darkMode ? darkTheme : lightTheme}>
      <AppContainer>
        <HeaderWrapper>
          <Header onNewProject={showProjectInitializer} onToggleTheme={toggleTheme} darkMode={darkMode} />
        </HeaderWrapper>
        <MainContent>
          <Sidebar activeModule={activeModule} onModuleChange={setActiveModule} />
          <ContentArea>
            {activeModule === 'new-project' ? (
              <>
                {showInitializer ? (
                  <ProjectInitializer
                    onClose={handleCloseInitializer}
                    onGenerate={handleGeneratedTree}
                    darkMode={darkMode}
                  />
                ) : (
                  <div style={{
                    display: 'flex',
                    gap: '20px',
                    flex: 1,
                    height: 'calc(100vh - 200px)',
                    overflow: 'hidden'
                  }}>
                    <TreeEditor treeData={treeData} updateTree={updateTree} darkMode={darkMode} />
                    <TreeOutput treeData={treeData} darkMode={darkMode} />
                  </div>
                )}
              </>
            ) : activeModule === 'prompt-optimizer' ? (
              <PromptOptimizer />
            ) : activeModule === 'prompt-analysis' ? (
              <PromptAnalysisPage />
            ) : (
              <PromptOptimizer />
            )}
          </ContentArea>
        </MainContent>
        <FooterWrapper>
          <Footer />
        </FooterWrapper>
      </AppContainer>
    </ThemeProvider>
  );
};

export default App; 