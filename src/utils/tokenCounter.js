/**
 * Token Counter Tool
 * 用于计算文本的token大小，支持中英文
 */

// 预加载的编码器
let encoders = {};

/**
 * Token计数器，用于计算文本的token大小
 */
class TokenCounter {
  /**
   * 初始化Token计数器
   * @param {string} model - 使用的模型名称，默认为gpt-3.5-turbo
   */
  constructor(model = "gpt-3.5-turbo") {
    this.model = model;
    this.encoding = null;
  }

  /**
   * 获取编码器
   * @returns {Promise<Object>} - 编码器对象
   */
  async getEncoding() {
    if (!this.encoding) {
      try {
        // 浏览器环境使用@dqbd/tiktoken
        const tiktoken = await import('@dqbd/tiktoken');
        this.encoding = tiktoken.encoding_for_model(this.model);
      } catch (error) {
        console.error('获取编码器失败:', error);
        // 使用默认编码器
        try {
          const tiktoken = await import('@dqbd/tiktoken');
          this.encoding = tiktoken.get_encoding("cl100k_base");
        } catch (fallbackError) {
          console.error('获取默认编码器也失败:', fallbackError);
          // 此时将使用近似计算方法
        }
      }
    }
    return this.encoding;
  }

  /**
   * 计算文本的token数量
   * @param {string} text - 需要计算token的文本
   * @returns {Promise<number>} - token数量
   */
  async countTokens(text) {
    if (!text) {
      return 0;
    }

    try {
      const encoding = await this.getEncoding();
      if (encoding) {
        const tokens = encoding.encode(text);
        return tokens.length;
      } else {
        // 如果无法获取编码器，使用近似计算方法
        return this.approximateTokenCount(text);
      }
    } catch (error) {
      console.error('计算token失败:', error);
      // 使用近似计算方法作为后备
      return this.approximateTokenCount(text);
    }
  }

  /**
   * 近似计算token数量（当tiktoken不可用时的后备方法）
   * @param {string} text - 需要计算token的文本
   * @returns {number} - 近似token数量
   */
  approximateTokenCount(text) {
    if (!text) return 0;
    
    // 英文单词约等于1个token
    // 中文字符约等于1.5个token
    // 标点符号和空格约等于0.5个token
    
    // 匹配中文字符
    const chineseChars = (text.match(/[\u4e00-\u9fff]/g) || []).length;
    
    // 匹配英文单词
    const englishWords = (text.match(/[a-zA-Z]+/g) || []).length;
    
    // 匹配数字、标点和空格
    const otherChars = (text.match(/[0-9\s\p{P}]/gu) || []).length;
    
    // 计算总token数
    return Math.ceil(chineseChars * 1.5 + englishWords + otherChars * 0.5);
  }
}

/**
 * 创建一个默认的token计数器实例
 */
const defaultTokenCounter = new TokenCounter();

/**
 * 计算文本的token数量（便捷函数）
 * @param {string} text - 需要计算token的文本
 * @param {string} model - 使用的模型名称，默认为gpt-3.5-turbo
 * @returns {Promise<number>} - token数量
 */
export const countTokens = async (text, model = "gpt-3.5-turbo") => {
  if (model !== defaultTokenCounter.model) {
    const counter = new TokenCounter(model);
    return counter.countTokens(text);
  }
  return defaultTokenCounter.countTokens(text);
};

export default TokenCounter;