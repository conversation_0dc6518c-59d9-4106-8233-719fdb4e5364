/**
 * 用于生成项目结构的Prompt模板
 */


// 项目类型选项
export const PROJECT_TYPES = {
  FRONTEND: 'frontend',
  BACKEND_JAVA: 'backend-java',
  BACKEND_PYTHON: 'backend-python',
  BACKEND_NODEJS: 'backend-nodejs',
  LLM_PYTHON: 'llm-python',
  FULLSTACK: 'fullstack',
  MOBILE: 'mobile',
  LIBRARY: 'library',
  PYTHON_ALGORITHM: 'python-algorithm',
  MCP_PYTHON: 'mcp-python',
};

// 项目类型显示名称
export const PROJECT_TYPE_LABELS = {
  [PROJECT_TYPES.FRONTEND]: '前端项目 (React/Vue)',
  [PROJECT_TYPES.BACKEND_JAVA]: '后端项目 (Java Spring Boot)',
  [PROJECT_TYPES.BACKEND_PYTHON]: '后端项目 (Python Flask/Django)',
  [PROJECT_TYPES.BACKEND_NODEJS]: '后端项目 (Node.js Express)',
  [PROJECT_TYPES.LLM_PYTHON]: 'LLM项目 (Python RAG/AI应用)',
  [PROJECT_TYPES.FULLSTACK]: '全栈项目',
  [PROJECT_TYPES.MOBILE]: '移动应用项目',
  [PROJECT_TYPES.LIBRARY]: '工具库/SDK',
  [PROJECT_TYPES.PYTHON_ALGORITHM]: 'Python算法项目',
  [PROJECT_TYPES.MCP_PYTHON]: 'MCP Server项目 (Python)',
};

/**
 * 基本项目结构生成Prompt模板
 * @param {string} projectType - 项目类型
 * @param {string} description - 项目描述
 * @param {string} additionalInfo - 额外信息
 * @returns {string} 格式化后的prompt
 */
export const createProjectStructurePrompt = (projectType, description, additionalInfo = '') => {
  
  const basicTemplate = `# 项目结构设计

## 项目基本信息
- **项目类型**：${PROJECT_TYPE_LABELS[projectType] || projectType}
- **项目描述**：${description}
${additionalInfo ? `- **额外信息**：${additionalInfo}` : ''}

## 🎯 核心能力分析 (必须完成，这是设计的基础)

**重要说明**：在设计项目结构之前，你必须首先深入分析项目描述，识别出这个具体业务场景需要的核心能力。不要使用通用的技术能力，而要基于业务场景分析出具体的功能能力。

### 分析步骤：
1. **业务场景理解**：仔细分析"${description}"这个项目描述，理解其业务背景和目标用户
2. **核心能力识别**：基于业务场景，列出3-8个具体的核心功能能力（不是技术能力）
3. **能力细化**：为每个核心能力说明其具体包含的子功能

### 示例分析（仅供参考格式，请基于实际项目描述进行分析）：
**项目**：面向商家的营销活动管理平台
**核心能力分析**：
- **商家账户与权限管理能力**：商家注册登录、多级权限控制、团队成员管理
- **营销活动策划与创建能力**：活动模板选择、活动规则配置、活动时间管理、活动预览
- **目标用户圈选与定向能力**：用户标签管理、圈选条件设置、用户群体分析、精准投放
- **营销内容与素材管理能力**：图片视频上传、文案编辑、素材库管理、内容审核
- **活动执行与监控能力**：活动发布、实时监控、异常处理、紧急停止
- **数据统计与效果分析能力**：实时数据看板、转化率分析、ROI计算、报表导出
- **消息推送与触达能力**：多渠道推送、消息模板、发送策略、触达统计

**请你现在基于"${description}"进行类似的具体分析，不要照搬示例。**

## 设计要求
1. **🔥 核心能力驱动设计**：项目结构必须清晰地反映上述核心能力分析的结果。每个核心能力都应该在目录结构中有明确的体现（如专门的模块、组件或服务）。
2. **业务逻辑优先**：优先体现业务功能的组织，而不是技术架构的组织。例如，应该有"营销活动管理"模块，而不只是"controllers"、"services"等技术层。
3. **功能模块化**：为每个核心能力设计对应的功能模块，确保代码组织与业务能力一一对应。
4. 提供一个详尽而规范的目录结构，包含所有必要的文件和文件夹。
5. 确保每个文件夹和文件都有具体的功能描述，说明其用途、职责、**如何支持某个具体的核心能力**，以及与其他组件的关系。
6. 遵循行业公认的项目组织模式和设计原则，如关注点分离、模块化、可测试性等。
7. 结构应该清晰地展示项目的架构及各组件间的关系，**特别是核心能力是如何通过各组件协作实现的**。
8. 包含必要的配置文件、文档文件和测试目录。
9. 适当考虑项目的可扩展性、可维护性和协作开发。
10. 所有功能描述必须使用中文，不要使用英文或其他语言。
11. 文件夹名和文件名应符合编程规范（如驼峰命名、蛇形命名等），特殊的技术名词可保留英文。

## 输出格式
返回格式必须为一个JSON对象，严格遵循以下结构：
\`\`\`json
{
  "name": "项目根目录名称",
  "type": "folder",
  "capabilities": "详细的项目根目录功能描述，说明项目如何支持所分析的核心能力",
  "children": [
    {
      "name": "子目录或文件名",
      "type": "folder或file",
      "capabilities": "详细的功能描述，包括该组件的具体职责、作用，以及它如何服务于项目的某个核心能力",
      "children": [...]  // 如果是文件夹且有子项
    },
    ...
  ]
}
\`\`\`

请确保capabilities字段对每个文件和文件夹都有详尽而具体的中文描述，避免使用过于简单或笼统的表述。对于关键文件，应详细说明其在项目中的作用和重要性，**以及它如何贡献于项目的核心功能实现**。

**特别强调**：输出的项目结构应该让人一眼就能看出这是一个"${description}"项目，而不是一个通用的技术项目。`;

  // 特定项目类型的自定义模板
  if (projectType === PROJECT_TYPES.FRONTEND) {
    return `# 前端项目结构设计

## 项目基本信息
- **项目类型**：前端项目 (React/Vue)
- **项目描述**：${description}
${additionalInfo ? `- **额外信息**：${additionalInfo}` : ''}

## 🎯 核心能力分析 (必须完成，这是设计的基础)

**重要说明**：在设计前端项目结构之前，你必须首先深入分析项目描述，识别出这个具体业务场景需要的核心前端功能能力。不要使用通用的技术能力（如"组件管理"、"状态管理"），而要基于业务场景分析出具体的用户功能能力。

### 分析步骤：
1. **业务场景理解**：仔细分析"${description}"这个项目描述，理解其业务背景和目标用户
2. **用户功能识别**：基于业务场景，列出3-8个具体的用户功能能力（用户能做什么）
3. **功能细化**：为每个核心能力说明其具体包含的用户交互和界面需求

### 示例分析（仅供参考格式，请基于实际项目描述进行分析）：
**项目**：面向商家的营销活动管理平台
**核心能力分析**：
- **商家登录与账户管理界面**：登录注册页面、个人信息管理、权限设置界面
- **营销活动创建与编辑界面**：活动创建向导、规则配置表单、活动预览界面
- **用户圈选与定向配置界面**：标签选择器、条件构建器、用户群体预览
- **营销素材管理界面**：素材上传组件、素材库浏览、编辑器集成
- **活动监控与控制界面**：实时状态看板、控制按钮、异常告警界面
- **数据分析与报表界面**：图表展示组件、数据筛选器、报表导出功能
- **消息推送配置界面**：推送模板编辑、渠道选择、发送策略配置

**请你现在基于"${description}"进行类似的具体分析，不要照搬示例。**

## 设计要求
1. **🔥 核心能力驱动设计**：项目结构必须清晰地反映上述核心能力分析的结果。每个核心能力都应该在目录结构中有明确的体现（例如，在\`src/modules\`、\`src/features\`或\`src/pages\`下为每个核心能力创建对应的模块）。
2. **业务功能优先**：优先体现业务功能的组织，而不是技术架构的组织。例如，应该有"营销活动管理"、"用户圈选"等业务模块，而不只是"components"、"utils"等技术层。
3. **用户体验导向**：结构应该体现用户的使用流程和功能需求，便于开发者理解业务逻辑。
4. 提供一个现代化的前端项目目录结构，完整且规范。
5. 确保每个文件夹和文件都有详细具体的功能描述，解释其用途、重要性，**以及它如何支持特定的核心能力**。
6. 遵循组件化、模块化的设计理念，体现前端工程化思想。
7. 考虑状态管理、路由管理、组件库、样式管理、API调用等核心功能的组织方式，**并说明它们如何服务于核心能力的实现**。
8. 包含适当的测试文件夹和配置文件，考虑CI/CD的集成。
9. 结构应符合React/Vue等现代前端框架的最佳实践。
10. 考虑性能优化、代码分割、懒加载等现代前端技术的应用。
11. 所有功能描述必须使用中文，不要使用英文或其他语言。
12. 文件夹名和文件名应符合前端编程规范（如驼峰命名），特殊的技术名词可保留英文。

## 输出格式
返回格式必须为JSON对象，结构如下：
\`\`\`json
{
  "name": "项目根目录名称",
  "type": "folder",
  "capabilities": "详细的项目根目录功能描述，说明项目整体如何支持所分析的核心能力",
  "children": [
    {
      "name": "子目录或文件名",
      "type": "folder或file",
      "capabilities": "详细的功能描述，包括该组件的具体职责、作用，以及它如何服务于项目的某个核心能力",
      "children": [...]  // 如果是文件夹且有子项
    }
  ]
}
\`\`\`

对于重要的配置文件(如package.json、webpack配置、环境变量配置等)、核心源码目录（特别是体现核心能力的模块）、组件文件夹等，请提供特别详细的中文描述，说明它们如何协同工作以及在项目中扮演的角色，**以及它们如何支持各核心能力的实现**。对于目录和文件的命名，应遵循前端项目的常见规范，关键的框架特定文件（如App.jsx、main.js、index.html）保持规范的命名。

**特别强调**：输出的项目结构应该让人一眼就能看出这是一个"${description}"项目，而不是一个通用的前端技术项目。`;
  } else if (projectType === PROJECT_TYPES.BACKEND_JAVA) {
    return `# Java Spring Boot项目结构设计

## 项目基本信息
- **项目类型**：Java Spring Boot后端项目
- **项目描述**：${description}
${additionalInfo ? `- **额外信息**：${additionalInfo}` : ''}

## 🎯 核心能力分析 (必须完成，这是设计的基础)

**重要说明**：在设计后端项目结构之前，你必须首先深入分析项目描述，识别出这个具体业务场景需要的核心后端服务能力。不要使用通用的技术能力（如"数据访问"、"业务逻辑处理"），而要基于业务场景分析出具体的服务能力。

### 分析步骤：
1. **业务场景理解**：仔细分析"${description}"这个项目描述，理解其业务背景和服务对象
2. **服务能力识别**：基于业务场景，列出3-8个具体的核心服务能力（后端需要提供什么服务）
3. **能力细化**：为每个核心能力说明其具体包含的API接口和业务逻辑

### 示例分析（仅供参考格式，请基于实际项目描述进行分析）：
**项目**：订单管理系统
**核心能力分析**：
- **用户认证与授权服务能力**：用户登录验证、权限控制、Token管理、角色分配
- **商品信息管理服务能力**：商品CRUD操作、库存管理、价格策略、商品分类
- **订单处理与状态管理能力**：订单创建、状态流转、订单查询、订单取消
- **库存管理与预占能力**：库存查询、库存预占、库存释放、库存告警
- **支付集成与对账能力**：支付接口集成、支付状态同步、对账处理、退款处理
- **数据统计与报表服务能力**：销售统计、用户行为分析、业务报表生成

**请你现在基于"${description}"进行类似的具体分析，不要照搬示例。**

## 设计要求
1. **🔥 核心能力驱动设计**：项目结构（尤其是包结构）必须清晰地反映核心能力。例如，可以为每个核心能力设计专门的模块或包 (e.g., \`com.company.project.ordermanagement\`, \`com.company.project.inventorymanagement\`)。
2. **业务服务优先**：优先体现业务服务的组织，而不是技术架构的组织。例如，应该有"订单管理服务"、"库存管理服务"等业务模块，而不只是"controller"、"service"、"repository"等技术层。
3. **领域驱动设计**：采用DDD思想，围绕业务领域和核心能力组织代码结构。
4. 提供一个完整的符合Maven/Gradle标准的Java Spring Boot项目目录结构。
5. 每个目录和文件都应有详细的功能描述，明确说明其用途、职责，**以及它如何支持特定的核心能力**。
6. 遵循领域驱动设计(DDD)或清晰的分层架构（控制层、服务层、数据访问层等），**并说明这些层次如何组织以实现核心能力**。
7. 包含必要的配置文件、资源文件、测试目录和文档。
8. 考虑错误处理、日志记录、安全配置、数据库访问等关键后端功能，**并说明它们如何保障核心能力的稳定运行**。
9. 体现微服务架构思想(如适用)，**每个微服务应对应一个或多个核心能力**。
10. 考虑容器化部署相关配置。
11. 所有功能描述必须使用中文，不要使用英文或其他语言。
12. 文件夹名和文件名应符合Java编程规范（如包名全小写，类名驼峰），特殊的技术名词可保留英文。

## 输出格式
返回格式必须为JSON对象：
\`\`\`json
{
  "name": "项目根目录名称",
  "type": "folder",
  "capabilities": "详细的项目根目录功能描述，说明项目整体架构如何支持分析出的核心能力",
  "children": [...]
}
\`\`\`

确保对每个目录和文件的功能描述都非常具体和专业，不要使用模糊或通用的描述。特别是针对Controller、Service、Repository、Entity等核心组件，以及application.properties/application.yml、pom.xml/build.gradle等重要配置文件，应详细说明其在整个架构中的作用、配置要点，**以及它们如何为核心能力的实现做出贡献**。

对于Java特有的文件组织方式，如源代码的包结构(com.company.project.*)，请遵循Java命名规范，同时提供清晰的包划分原则和模块边界说明，**重点说明包结构如何映射到项目核心能力**。

**特别强调**：输出的项目结构应该让人一眼就能看出这是一个"${description}"项目，而不是一个通用的Spring Boot技术项目。`;
  } else if (projectType === PROJECT_TYPES.BACKEND_PYTHON) {
    return `# Python后端项目结构设计

## 项目基本信息
- **项目类型**：Python后端项目 (Flask/Django/FastAPI)
- **项目描述**：${description}
${additionalInfo ? `- **额外信息**：${additionalInfo}` : ''}

## 🎯 核心能力分析 (必须完成，这是设计的基础)

**重要说明**：在设计Python后端项目结构之前，你必须首先深入分析项目描述，识别出这个具体业务场景需要的核心后端服务能力。不要使用通用的技术能力（如"数据访问"、"API接口"），而要基于业务场景分析出具体的服务能力。

### 分析步骤：
1. **业务场景理解**：仔细分析"${description}"这个项目描述，理解其业务背景和服务对象
2. **服务能力识别**：基于业务场景，列出3-8个具体的核心服务能力（后端需要提供什么服务）
3. **能力细化**：为每个核心能力说明其具体包含的API接口和业务逻辑

### 示例分析（仅供参考格式，请基于实际项目描述进行分析）：
**项目**：内容管理系统
**核心能力分析**：
- **内容发布与编辑服务能力**：文章创建、内容编辑、草稿保存、发布审核、版本管理
- **用户评论与互动服务能力**：评论发布、回复管理、点赞收藏、举报处理、互动统计
- **内容分类与标签管理能力**：分类创建、标签管理、内容归类、搜索优化、推荐算法
- **用户权限与角色管理能力**：用户注册、权限分配、角色管理、访问控制、安全认证
- **API接口与数据服务能力**：RESTful API、数据导出、第三方集成、接口文档、性能监控

**请你现在基于"${description}"进行类似的具体分析，不要照搬示例。**

## 设计要求
1. **🔥 核心能力驱动设计**：项目结构（例如Django的apps或Flask的blueprints）必须清晰地反映核心能力。每个核心能力应该有对应的模块或应用。
2. **业务服务优先**：优先体现业务服务的组织，而不是技术架构的组织。例如，应该有"内容管理"、"用户互动"等业务模块，而不只是"views"、"models"、"utils"等技术层。
3. **模块化设计**：围绕核心能力进行模块划分，确保每个模块职责单一且边界清晰。
4. 提供一个完整的符合Python最佳实践的后端项目目录结构。
5. 每个目录和文件都应有详细而具体的功能描述，**说明其如何支持某个核心能力**。
6. 遵循PEP 8编码规范和Python项目组织原则。
7. 清晰区分视图/控制器、模型、服务层、工具函数等不同组件，**并阐述它们如何协同实现核心能力**。
8. 包含测试目录、配置文件、数据库迁移文件等必要组件。
9. 考虑错误处理、中间件、身份认证、ORM层等关键功能，**并说明它们如何支持核心能力的稳定和安全**。
10. 遵循RESTful API设计原则(如适用)，API端点应与核心能力对应。
11. 文件夹名和文件名应符合Python编程规范（如蛇形命名），特殊的技术名词可保留英文。

## 输出格式
返回格式必须为JSON对象：
\`\`\`json
{
  "name": "项目根目录名称",
  "type": "folder",
  "capabilities": "详细的项目根目录功能描述，说明项目整体架构如何支持分析出的核心能力",
  "children": [...]
}
\`\`\`

确保对诸如app.py/wsgi.py、models.py、views.py、urls.py、settings.py等关键文件，以及templates、static、migrations等重要目录的功能描述非常具体和专业。解释它们如何协同工作以及在整个后端架构中扮演的角色，**并重点说明它们如何服务于核心能力的实现**。

对于Python Web框架特有的目录结构，如Django的apps、Flask的blueprints、FastAPI的routers等，请提供详细的组织方式说明，**强调这种组织方式如何支持项目的核心能力划分**，同时考虑数据库访问、缓存、异步任务、API文档等常见后端需求的解决方案。

**特别强调**：输出的项目结构应该让人一眼就能看出这是一个"${description}"项目，而不是一个通用的Python Web技术项目。`;
  } else if (projectType === PROJECT_TYPES.LLM_PYTHON) {
    return `# LLM应用项目结构设计

## 项目基本信息
- **项目类型**：LLM应用项目 (Python)
- **项目描述**：${description}
${additionalInfo ? `- **额外信息**：${additionalInfo}` : ''}

## 核心能力分析 (请根据项目描述，首先分析并列出本项目需要实现的核心LLM相关能力及业务能力)
例如，一个智能客服RAG系统可能需要：
- **LLM能力**：文档解析与分块、向量化与存储、相似性检索、提示词构建与优化、LLM调用与响应生成、对话历史管理
- **业务能力**：用户意图识别、知识库管理、客服工单对接

## 设计要求
1. **基于核心能力设计**：项目结构需清晰体现LLM核心组件和业务逻辑模块的划分，确保每个分析出的核心能力都有对应的代码组织。
2. 设计一个专业的大语言模型(LLM)应用项目结构，适用于多种场景：
   - 检索增强生成(RAG)系统
   - 对话式AI应用
   - 大模型微调与训练
   - AI代理(Agent)系统
   - 多模态LLM应用(文本、图像、音频等)
   - 垂直领域的专业LLM应用
   - LLM评估与分析系统
   - LLM API服务与中间件
3. 遵循现代Python项目结构最佳实践，并特别针对LLM应用的特点进行优化。
4. 包含数据处理管道、模型管理、向量数据库集成等关键LLM组件，**并说明它们如何支持具体的核心LLM能力**。
5. 考虑模型部署、API服务、性能监控等生产环境需求，**并关联到核心能力的稳定运行**。
6. 提供清晰的模块分离，便于团队协作和代码维护，**特别是围绕核心能力进行模块划分**。
7. 确保可扩展性，支持不同的LLM提供商和模型。
8. 包含必要的评估框架和测试组件。
9. 所有功能描述必须使用中文，不要使用英文或其他语言。

## 输出格式
返回格式必须为JSON对象：
\`\`\`json
{
  "name": "项目根目录名称",
  "type": "folder",
  "capabilities": "详细的项目根目录功能描述，说明项目如何组织以实现分析出的各项核心LLM及业务能力",
  "children": [...]
}
\`\`\`

特别关注以下LLM应用核心组件的组织和描述，**并明确它们各自支持哪些核心能力**：
- 数据预处理和向量化组件
- 检索和相似性搜索模块
- 提示词工程和模板管理
- 模型接口和适配器层
- 对话管理和上下文处理
- 评估指标和性能监控
- 部署配置和服务化组件

确保每个组件的功能描述都体现出对LLM应用领域的深入理解，并提供具体的技术实现思路，**以及它如何为项目核心能力的实现贡献价值**。`;
  } else if (projectType === PROJECT_TYPES.PYTHON_ALGORITHM) {
    return `# Python算法项目结构设计

## 项目基本信息
- **项目类型**：Python算法项目
- **项目描述**：${description}
${additionalInfo ? `- **额外信息**：${additionalInfo}` : ''}

## 核心能力分析 (请根据项目描述，首先分析并列出本项目需要实现的核心算法能力及相关业务目标)
例如，一个图像分类算法项目可能需要：
- **算法能力**：图像数据增强、特征提取网络构建、分类模型训练、模型评估与选择、模型可解释性分析
- **业务目标**：实现对特定类别图像的高精度自动分类

## 设计要求
1. **基于核心能力设计**：项目结构需清晰反映算法开发流程的各个阶段，并能支撑核心算法能力的实现与迭代。
2. 设计一个专业的Python算法项目结构，适用于机器学习、深度学习、数据科学等算法开发。
3. 遵循科学计算和机器学习项目的最佳实践。
4. 包含数据处理、特征工程、模型训练、评估验证等关键组件，**并说明它们如何支持核心算法能力的构建**。
5. 提供清晰的实验管理和结果跟踪机制，**能够记录不同参数配置下核心能力的表现**。
6. 支持模型版本控制和实验复现。
7. 包含完整的数据管道和模型部署准备。
8. 考虑算法性能优化和可视化展示，**特别是针对核心算法能力的性能和效果**。
9. 所有功能描述必须使用中文，不要使用英文或其他语言。

## 输出格式
返回格式必须为JSON对象：
\`\`\`json
{
  "name": "项目根目录名称",
  "type": "folder",
  "capabilities": "详细的项目根目录功能描述，说明项目结构如何支持核心算法能力的研发和业务目标的达成",
  "children": [...]
}
\`\`\`

特别关注以下算法项目核心组件，**并明确它们各自支持哪些核心算法能力**：
- 数据获取和预处理模块
- 特征工程和数据增强
- 模型定义和训练脚本
- 超参数调优和实验管理
- 评估指标和性能分析
- 可视化和结果展示
- 模型导出和部署准备

确保结构体现算法开发的完整流程，从数据到模型再到部署的全链路考虑，**始终围绕核心算法能力和业务目标进行组织**。`;
  }

  return basicTemplate;
};

/**
 * 基于GitHub仓库结构生成项目结构的Prompt模板
 * @param {string} projectType - 项目类型
 * @param {string} description - 项目描述
 * @param {Object} repoStructure - GitHub仓库结构
 * @param {string} additionalInfo - 额外信息
 * @returns {string} 格式化后的prompt
 */
export const createStructureFromRepoPrompt = (projectType, description, repoStructure, additionalInfo = '') => {
  const projectTypeLabel = PROJECT_TYPE_LABELS[projectType] || '未知类型项目';

  return `# 基于参考仓库的项目结构设计

    ## 项目基本信息
    - **项目类型**：${projectTypeLabel}
    - **项目描述**：${description}
    ${additionalInfo ? `- **额外信息**：${additionalInfo}` : ''}

    ## 核心能力分析 (请根据项目描述，首先分析并列出本项目需要实现的核心功能模块/能力点。然后对照参考仓库结构，判断哪些能力可以借鉴，哪些需要新增或调整)
    示例：
    - 项目核心能力A (参考仓库已有良好实践)
    - 项目核心能力B (参考仓库部分涉及，需要强化)
    - 项目核心能力C (项目特有，参考仓库未包含，需新增设计)


    ## 参考仓库结构
    以下是从GitHub仓库获取的项目结构，请以此为参考进行优化和完善：

    \`\`\`
    ${JSON.stringify(repoStructure, null, 2)}
    \`\`\`

    ## 设计要求
    1. **基于核心能力设计与参考结构优化**：首先，基于项目描述分析出的核心能力，审视参考仓库结构。项目最终结构必须清晰地反映这些核心能力。
    2. 保持参考结构中与核心能力相关的优秀组织理念，并根据当前项目需求进行调整。
    3. 确保每个文件夹和文件都有详细具体的功能描述，**说明其如何支持特定的核心能力，以及与参考结构中对应部分的关系（是保留、修改还是新增）**。
    4. 添加参考结构中可能缺失但当前项目核心能力需要的组件。
    5. 移除或调整参考结构中不适合当前项目核心能力的部分。
    6. 所有功能描述必须使用中文，详细说明各组件的用途、重要性，**以及它们如何服务于核心能力的实现**。
    7. 保持良好的项目组织结构和可维护性。

    ## 输出格式
    返回格式必须为JSON对象，严格遵循以下结构：
    \`\`\`json
    {
      "name": "项目根目录名称",
      "type": "folder",
      "capabilities": "详细的项目根目录功能描述，说明项目整体如何基于参考结构并围绕核心能力进行组织",
      "children": [
        {
          "name": "子目录或文件名",
          "type": "folder或file",
          "capabilities": "详细的功能描述，包括该组件的具体职责、作用，它如何服务于项目的某个核心能力，以及它与参考仓库对应部分的关系",
          "children": [...]  // 如果是文件夹且有子项
        }
      ]
    }
    \`\`\`

    请确保输出的结构既借鉴了参考仓库的优秀组织方式（特别是与核心能力相关的部分），又针对具体的项目需求和核心能力进行了合理的优化和完善。明确指出哪些部分是基于核心能力分析而新增或修改的。`;
};

/**
 * 基于上传文件夹结构生成项目结构的Prompt模板
 * @param {string} projectType - 项目类型
 * @param {string} description - 项目描述
 * @param {Object} folderStructure - 上传的文件夹结构
 * @param {string} additionalInfo - 额外信息
 * @returns {string} 格式化后的prompt
 */
export const createStructureFromFolderPrompt = (projectType, description, folderStructure, additionalInfo = '') => {
  const projectTypeLabel = PROJECT_TYPE_LABELS[projectType] || '未知类型项目';

  return `# 基于参考仓库的项目结构设计

    ## 项目基本信息
    - **项目类型**：${projectTypeLabel}
    - **项目描述**：${description}
    ${additionalInfo ? `- **额外信息**：${additionalInfo}` : ''}

    ## 核心能力分析 (请根据项目描述，首先分析并列出本项目需要实现的核心功能模块/能力点。然后对照参考仓库结构，判断哪些能力可以借鉴，哪些需要新增或调整)
    示例：
    - 项目核心能力A (参考仓库已有良好实践)
    - 项目核心能力B (参考仓库部分涉及，需要强化)
    - 项目核心能力C (项目特有，参考仓库未包含，需新增设计)


    ## 参考仓库结构
    以下是获取的项目结构，请以此为参考进行优化和完善：

    \`\`\`
    ${JSON.stringify(folderStructure, null, 2)}
    \`\`\`

    ## 设计要求
    1. **基于核心能力设计与参考结构优化**：首先，基于项目描述分析出的核心能力，审视参考仓库结构。项目最终结构必须清晰地反映这些核心能力。
    2. 保持参考结构中与核心能力相关的优秀组织理念，并根据当前项目需求进行调整。
    3. 确保每个文件夹和文件都有详细具体的功能描述，**说明其如何支持特定的核心能力，以及与参考结构中对应部分的关系（是保留、修改还是新增）**。
    4. 添加参考结构中可能缺失但当前项目核心能力需要的组件。
    5. 移除或调整参考结构中不适合当前项目核心能力的部分。
    6. 所有功能描述必须使用中文，详细说明各组件的用途、重要性，**以及它们如何服务于核心能力的实现**。
    7. 保持良好的项目组织结构和可维护性。

    ## 输出格式
    返回格式必须为JSON对象，严格遵循以下结构：
    \`\`\`json
    {
      "name": "项目根目录名称",
      "type": "folder",
      "capabilities": "详细的项目根目录功能描述，说明项目整体如何基于参考结构并围绕核心能力进行组织",
      "children": [
        {
          "name": "子目录或文件名",
          "type": "folder或file",
          "capabilities": "详细的功能描述，包括该组件的具体职责、作用，它如何服务于项目的某个核心能力，以及它与参考仓库对应部分的关系",
          "children": [...]  // 如果是文件夹且有子项
        }
      ]
    }
    \`\`\`

    请确保输出的结构既借鉴了参考仓库的优秀组织方式（特别是与核心能力相关的部分），又针对具体的项目需求和核心能力进行了合理的优化和完善。明确指出哪些部分是基于核心能力分析而新增或修改的。`;
};
