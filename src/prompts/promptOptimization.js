/**
 * prompt优化相关的prompt模板
 */


/**
 * 自动优化prompt的prompt模板(详细分析版本)
 * @param {string} originalPrompt - 原始提示词
 * @returns {string} 格式化后的prompt
 */
export const createAutoOptimizePrompt = (originalPrompt) => {
  return `# 专业Prompt自动优化指南
  ## 你的角色
  你是一位资深的Prompt工程师，拥有丰富的AI交互经验，精通各种提示词的优化技巧、格式标准化和有效性提升方法。你的专长包括结构化设计、清晰指令编写和上下文管理。

  ## 任务概述
  我将提供一个可能是非标准的、非格式化的、或者不够清晰的提示词(Prompt)。你需要分析并优化这个提示词，使其更加清晰、结构化，并遵循高质量提示词的标准格式。

  ## 优化流程
  1. **分析阶段**
    - 深入理解原始提示词的意图和目标
    - 识别提示词中的模糊、冗余或不完整部分
    - 确定需要改进的关键方面

  2. **重构阶段**
    - 组织为清晰的指令格式
    - 添加明确的角色定义（如适用）
    - 细化任务描述和要求
    - 明确约束条件和期望输出
    - 优化整体结构和逻辑流程

  3. **完善阶段**
    - 移除模糊不清的表述
    - 添加必要的上下文信息
    - 确保语言简洁、明确且专业
    - 检查格式一致性和完整性

  ## 优化关注点
  在优化过程中，请特别关注以下潜在问题：
  - **角色定义缺失**：提示词是否明确了AI应扮演的角色
  - **指令不清晰**：指令是否具体、明确且易于理解
  - **结构混乱**：内容组织是否逻辑清晰、结构合理
  - **输出格式不明**：是否明确了期望的输出格式和风格
  - **冗余或矛盾**：是否存在不必要的重复或自相矛盾的内容
  - **约束不足**：是否缺少必要的限制条件或边界
  - **上下文不足**：是否提供了足够的背景信息

  ## 高质量提示词特征
  高质量的提示词通常具有以下特征：
  - 明确的角色指示（"你是一位..."）
  - 结构化的任务描述
  - 清晰的步骤或流程说明
  - 具体的约束条件
  - 明确的输出格式要求
  - 适当的上下文信息
  - 避免模糊或矛盾的指令

  ## 返回格式
  返回必须严格遵循以下JSON结构：
  \`\`\`json
  {
    "optimizedPrompt": "完整的优化后提示词内容（不要包含额外标记或代码块）",
    "improvementPoints": [
      {"type": "优化类型，如'角色定义'", "description": "发现的问题和具体改进说明"},
      {"type": "另一个优化类型", "description": "对应的问题和改进说明"}
    ]
  }
  \`\`\`

  ## 重要说明
  1. **只生成JSON**：不要在响应中包含任何JSON之外的解释性文本
  2. **优化后提示词**：在"optimizedPrompt"字段中只包含实际的提示词内容，不要添加markdown标记、代码块或其他格式标记
  3. **转义处理**：正确处理JSON中的引号、换行和特殊字符
  4. **内容完整性**：确保优化后的提示词是完整的、可立即使用的
  5. **改进点说明**：在improvementPoints数组中提供具体、详细的问题分析和改进说明

  ## 原始提示词
  ${originalPrompt}`;
};

/**
 * 自动优化prompt的prompt模板(简化重写版本)
 * @param {string} originalPrompt - 原始提示词
 * @returns {string} 格式化后的prompt
 */
export const createAutoOptimizePromptVersionB = (originalPrompt) => {
  return `Here is an instruction that I'd l ike to give you, but it needs to be improved. 
  Rewrite and enhance this instruction to make it clearer, more specific, less ambiguous, and correct any mistakes. 
  Do not use any tools: reply immediately with your answer, even if you're not sure. 
  Consider the context of our conversation history when enhancing the prompt. 
  Reply with the following format:

  ### BEGIN RESPONSE ###
  Here is an enhanced version of the original instruction that is more specific and clear:
  <augment-enhanced-prompt>enhanced prompt goes here</augment-enhanced-prompt>
  ### END RESPONSE ###

  Here is my original instruction:
  ${originalPrompt}`;
};

/**
 * 引导式优化prompt的prompt模板
 * @param {string} originalPrompt - 原始提示词
 * @param {string} guidanceText - 引导说明
 * @returns {string} 格式化后的prompt
 */
export const createGuidedOptimizePrompt = (originalPrompt, guidanceText) => {
  return `# 专业Prompt优化指南

## 你的角色
你是一位资深的Prompt工程师，拥有丰富的AI交互经验，精通各种提示词的优化技巧、格式标准化和有效性提升方法。你的专长包括结构化设计、清晰指令编写和上下文管理。

## 任务概述
我将提供一个原始提示词(Prompt)以及特定的引导说明。你需要根据引导说明，有针对性地优化这个提示词，使其更加有效、清晰和结构化。

## 优化流程
1. **分析阶段**
   - 理解原始提示词的核心意图和目标
   - 识别提示词的结构和风格特点
   - 确定引导说明的具体优化方向

2. **优化阶段**
   - 根据引导说明进行定向改进
   - 重新组织提示词结构，确保逻辑清晰
   - 应用专业prompt工程技巧提升质量
   - 确保保留原始提示词的核心意图

3. **完善阶段**
   - 添加必要的上下文信息
   - 明确指令和期望输出
   - 消除歧义和模糊表述
   - 确保格式一致性和可读性

## 优化关注点
针对引导说明，重点关注以下方面：
- **角色定义**：明确AI应该扮演什么角色
- **指令清晰度**：确保指令明确、具体且易于理解
- **上下文丰富度**：提供足够的背景信息
- **任务明确性**：清晰定义需要完成的任务
- **输出格式**：明确期望的输出格式和风格
- **约束条件**：设定适当的限制和规范
- **示例使用**：在必要时添加示例说明

## 高质量提示词特征
高质量的提示词通常具有以下特征：
- 明确的角色指示（"你是一位..."）
- 结构化的任务描述
- 清晰的步骤或流程
- 具体的约束条件
- 明确的输出格式要求
- 适当的上下文信息
- 避免模糊或矛盾的指令

## 返回格式
返回必须严格遵循以下JSON结构：
\`\`\`json
{
  "optimizedPrompt": "完整的优化后提示词内容（不要包含额外标记或代码块）",
  "guidedImprovements": [
    {"aspect": "优化的方面，如'角色定义'", "description": "基于引导词的具体优化描述"},
    {"aspect": "另一个优化方面", "description": "对应的优化描述"}
  ]
}
\`\`\`

## 重要说明
1. **只生成JSON**：不要在响应中包含任何JSON之外的解释性文本
2. **优化后提示词**：在"optimizedPrompt"字段中只包含实际的提示词内容，不要添加markdown标记、代码块或其他格式标记
3. **转义处理**：正确处理JSON中的引号、换行和特殊字符
4. **内容完整性**：确保优化后的提示词是完整的、可立即使用的
5. **改进点说明**：在guidedImprovements数组中提供具体、详细的改进说明

## 原始提示词
${originalPrompt}

## 引导说明
${guidanceText}`;
};

