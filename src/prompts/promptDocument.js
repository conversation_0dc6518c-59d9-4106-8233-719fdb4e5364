/**
 * 用于生成项目文档的Prompt模板
 */

/**
 * 创建项目README文档的Prompt模板
 * @param {Object} projectStructure - 项目结构
 * @param {string} description - 项目描述
 * @returns {string} 格式化后的prompt
 */
const createProjectDocumentationPrompt = (projectStructure, description) => {
  return `请作为一位资深技术文档专家，基于以下项目结构生成一个专业的README.md文档：
   
	项目描述：${description}

	项目结构：
	${JSON.stringify(projectStructure, null, 2)}

	请遵循以下要求：
	1. 创建一个格式清晰、内容全面的README.md文档
	2. 文档语言应专业、准确且易于理解
	3. 适当使用Markdown格式化元素，如标题、列表、代码块、表格等
	4. 结构应有逻辑性，从基本介绍到详细使用指南

	文档应包含以下部分：
	1. 项目标题和徽章(如有)
	2. 项目简介：简明扼要地描述项目的目的和核心功能
	3. 特性列表：列出项目的主要功能和特点
	4. 技术栈：详细列出项目使用的所有主要技术、框架和库
	5. 项目结构：提供清晰的目录结构说明，重点解释核心组件的用途
	6. 安装指南：包含所有必要的步骤，确保新用户能轻松设置项目
	7. 使用说明：提供详细的使用示例，可能包含代码片段和截图
	8. API文档：如果适用，简要概述主要API端点
	9. 配置说明：解释关键配置选项和环境变量
	10. 测试指南：如何运行测试和验证功能
	11. 部署说明：如何将项目部署到生产环境
	12. 贡献指南：如何为项目做出贡献
	13. 许可证信息：明确项目的许可方式
	14. 致谢：感谢相关贡献者或使用的关键资源

	请确保文档内容直接基于提供的项目结构，准确反映其组织方式和功能设计。文档应对潜在用户和开发者有实际帮助，使他们能够快速理解并开始使用项目。`;
};

/**
 * 创建项目开发指南的Prompt模板
 * @param {Object} projectStructure - 项目结构
 * @param {string} description - 项目描述
 * @returns {string} 格式化后的prompt
 */
const createDevelopmentGuidePrompt = (projectStructure, description) => {
  return `请作为一位资深技术负责人，基于以下项目结构和描述生成一个全面的开发指南文档：

	项目描述：${description}

	项目结构：
	${JSON.stringify(projectStructure, null, 2)}

	请遵循以下要求：
	1. 创建一个详尽而结构清晰的开发指南，适合新加入团队的开发者快速上手
	2. 使用专业且准确的技术描述，同时保持内容易于理解
	3. 针对项目的具体技术栈和架构提供相关的最佳实践
	4. 文档应体现开发流程和工程化思想

	开发指南应包含以下部分：
	1. 项目概述：简要介绍项目的目标、架构和主要组件
	2. 技术栈详解：详细说明所使用的技术、框架和库，以及选择这些技术的原因
	3. 开发环境设置：
		- 所需软件和工具的安装与配置
		- 开发环境变量和配置项
		- 可能的IDE设置和推荐插件
	4. 架构详解：
		- 系统架构图和说明
		- 核心模块之间的关系和交互
		- 数据流程图(如适用)
	5. 代码组织规范：
		- 目录结构详细解释，每个关键目录和文件的作用
		- 命名约定和编码规范
		- 设计模式的应用
	6. 开发工作流程：
		- 分支管理策略和Git工作流
		- 代码审查流程和标准
		- 持续集成和部署流程
	7. 测试策略：
		- 单元测试、集成测试和端到端测试的编写指南
		- 测试覆盖率要求和最佳实践
	8. 调试和故障排除：
		- 常见问题及解决方案
		- 日志记录和监控策略
		- 性能分析和优化指南
	9. API文档和集成指南：
		- 内部API的使用方法
		- 与外部系统的集成点
	10. 安全最佳实践：
		- 安全编码指南
		- 数据保护和隐私考虑
	11. 部署和发布流程：
		- 环境管理(开发、测试、生产)
		- 发布检查清单
		- 回滚策略

	请确保指南内容与项目结构紧密结合，为开发团队提供清晰的指导，使他们能够高效地进行协作开发并遵循一致的标准。`;
};

export {
  createProjectDocumentationPrompt,
  createDevelopmentGuidePrompt,
}; 