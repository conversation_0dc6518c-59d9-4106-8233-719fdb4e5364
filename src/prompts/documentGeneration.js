/**
 * 文档生成相关的prompt模板
 */

/**
 * 项目文档生成的prompt模板
 * @param {Object} projectStructure - 项目结构对象
 * @param {string} description - 项目描述
 * @returns {string} 格式化后的prompt
 */
export const createProjectDocumentationPrompt = (projectStructure, description) => {
  return `# 项目README.md文档生成

## 任务说明
请根据提供的项目结构和描述，生成一个专业、详细、易读的README.md文档。

## 项目信息
- **项目描述**: ${description}
- **项目结构**: 
\`\`\`json
${JSON.stringify(projectStructure, null, 2)}
\`\`\`

## 文档要求
1. **结构完整**: 包含项目简介、功能特性、安装说明、使用方法、项目结构说明、贡献指南等标准章节
2. **内容专业**: 基于项目结构分析项目的技术栈、架构设计和核心功能
3. **格式规范**: 使用标准的Markdown格式，层次清晰，排版美观
4. **实用性强**: 提供具体可操作的安装、配置和使用步骤
5. **信息准确**: 确保所有信息与项目结构保持一致

## 文档章节结构
请按照以下结构组织README文档：

1. **项目标题和简介**
2. **主要功能特性**
3. **技术栈**
4. **快速开始**
   - 环境要求
   - 安装步骤
   - 配置说明
5. **项目结构说明**
6. **使用方法和示例**
7. **API文档（如适用）**
8. **开发指南**
9. **贡献指南**
10. **许可证**

## 输出要求
- 直接输出完整的Markdown格式文档内容
- 不要包含代码块标记，直接输出Markdown文本
- 确保所有链接、图片引用等格式正确
- 根据项目实际情况调整章节内容的详细程度`;
};

/**
 * 开发指南生成的prompt模板
 * @param {Object} projectStructure - 项目结构对象
 * @param {string} description - 项目描述
 * @returns {string} 格式化后的prompt
 */
export const createDevelopmentGuidePrompt = (projectStructure, description) => {
  return `# 项目开发指南生成

## 任务说明
请根据提供的项目结构和描述，生成一个详细的开发指南文档，帮助开发团队理解项目架构和开发流程。

## 项目信息
- **项目描述**: ${description}
- **项目结构**: 
\`\`\`json
${JSON.stringify(projectStructure, null, 2)}
\`\`\`

## 开发指南要求
1. **架构说明**: 详细分析项目的整体架构设计和模块划分
2. **开发环境**: 提供完整的开发环境搭建指南
3. **编码规范**: 基于项目技术栈提供相应的编码规范
4. **工作流程**: 说明开发、测试、部署的完整流程
5. **最佳实践**: 提供项目相关的开发最佳实践建议

## 文档章节结构
请按照以下结构组织开发指南：

1. **项目架构概述**
   - 整体架构设计
   - 核心模块说明
   - 数据流和交互关系

2. **开发环境设置**
   - 开发工具要求
   - 依赖安装
   - 环境配置

3. **项目结构详解**
   - 目录结构说明
   - 核心文件作用
   - 模块职责划分

4. **开发规范**
   - 代码风格指南
   - 命名约定
   - 注释规范

5. **开发流程**
   - 功能开发流程
   - 代码提交规范
   - 测试流程

6. **调试和故障排除**
   - 常见问题解决
   - 调试技巧
   - 日志分析

7. **部署指南**
   - 构建流程
   - 部署步骤
   - 环境配置

## 输出要求
- 输出完整的Markdown格式开发指南
- 内容应该具体可操作，避免过于抽象的描述
- 根据项目技术栈提供相应的技术细节
- 确保指南对新开发者友好，便于快速上手`;
};

/**
 * 文件样板代码生成的prompt模板
 * @param {Object} fileNode - 文件节点信息
 * @param {Object} projectStructure - 整个项目结构
 * @param {string} projectDescription - 项目描述
 * @returns {string} 格式化后的prompt
 */
export const createFileBoilerplatePrompt = (fileNode, projectStructure, projectDescription) => {
  // 根据文件扩展名确定文件类型
  const getFileType = (fileName) => {
    const ext = fileName.split('.').pop().toLowerCase();
    const typeMap = {
      'py': 'Python',
      'js': 'JavaScript',
      'jsx': 'React JSX',
      'ts': 'TypeScript',
      'tsx': 'React TypeScript',
      'java': 'Java',
      'json': 'JSON配置',
      'md': 'Markdown文档',
      'txt': '文本文件',
      'yml': 'YAML配置',
      'yaml': 'YAML配置',
      'xml': 'XML配置',
      'html': 'HTML',
      'css': 'CSS样式',
      'scss': 'SCSS样式',
      'sql': 'SQL脚本'
    };
    return typeMap[ext] || '未知类型';
  };

  const fileType = getFileType(fileNode.name);
  
  let prompt = `# 文件代码生成任务

## 文件信息
- **文件名**: ${fileNode.name}
- **文件类型**: ${fileType}
- **功能描述**: ${fileNode.capabilities || '未提供具体描述'}

## 项目上下文
- **项目描述**: ${projectDescription}
- **项目结构概览**:
\`\`\`json
${JSON.stringify(projectStructure, null, 2)}
\`\`\`

## 代码生成要求
1. **功能完整**: 根据文件描述和项目上下文生成完整可用的代码
2. **代码规范**: 遵循对应语言的编码规范和最佳实践
3. **注释完善**: 包含必要的函数、类和模块注释
4. **依赖处理**: 正确处理import/require等依赖声明
5. **结构清晰**: 代码结构清晰，逻辑合理

## 特定要求`;

  // 根据文件类型添加特定要求
  if (fileNode.name.endsWith('.py')) {
    prompt += `
- 遵循PEP 8编码规范
- 包含适当的docstring文档字符串
- 使用类型提示（Type Hints）
- 合理的异常处理
- 考虑模块的可重用性`;
  } else if (fileNode.name.endsWith('.js') || fileNode.name.endsWith('.jsx')) {
    prompt += `
- 使用现代JavaScript ES6+语法
- 包含JSDoc注释
- 合理的错误处理
- 如果是React组件，遵循React最佳实践
- 考虑性能优化`;
  } else if (fileNode.name.endsWith('.ts') || fileNode.name.endsWith('.tsx')) {
    prompt += `
- 使用TypeScript类型定义
- 包含完整的类型注解
- 遵循TypeScript最佳实践
- 如果是React组件，使用TypeScript的React类型
- 合理的泛型使用`;
  } else if (fileNode.name.endsWith('.java')) {
    prompt += `
- 遵循Java编码规范
- 包含完整的Javadoc注释
- 合理的异常处理机制
- 使用适当的设计模式
- 考虑线程安全（如适用）`;
  } else if (fileNode.name === 'README.md') {
    prompt += `
- 使用标准Markdown格式
- 包含项目介绍、安装、使用等标准章节
- 提供具体的代码示例
- 结构清晰，便于阅读
- 信息准确且实用`;
  } else if (fileNode.name === 'package.json') {
    prompt += `
- 包含合适的项目元信息
- 列出必要的依赖包和版本
- 提供实用的scripts脚本
- 合理的版本号和描述
- 适当的keywords和配置`;
  } else if (fileNode.name === 'requirements.txt') {
    prompt += `
- 列出项目所需的Python包
- 指定合适的版本范围
- 按重要性和功能分组
- 包含必要的开发依赖
- 考虑版本兼容性`;
  }

  prompt += `

## 输出要求
- 直接输出完整的文件内容，不要包含代码块标记
- 确保代码可以直接使用，没有语法错误
- 如果是配置文件，确保格式正确
- 如果是脚本文件，包含适当的执行权限说明
- 代码应该体现文件在整个项目中的角色和作用`;

  return prompt;
}; 