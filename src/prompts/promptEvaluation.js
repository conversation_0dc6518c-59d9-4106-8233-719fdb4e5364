/**
 * prompt质量评估相关的prompt模板
 * @param {string} originalPrompt - 原始提示词
 * @param {string} optimizedPrompt - 优化后的提示词
 * @returns {string} 格式化后的prompt
 */


export const createPromptEvaluationPrompt = (originalPrompt, optimizedPrompt) => {
  return `# Prompt质量评估专家
  ## 你的角色
  你是一位资深的Prompt工程师和AI交互专家，拥有丰富的提示词设计和评估经验。你能够客观、专业地分析和比较不同提示词的质量。

  ## 任务概述
  我将提供两个版本的提示词：原始版本和优化版本。你需要从多个维度对这两个提示词进行专业评估，并给出客观的质量分析。

  ## 评估维度
  请从以下8个关键维度对两个提示词进行评估（每个维度1-10分）：

  1. **具体性 (Specificity)**: 要求是否具体明确，避免模糊表述
     - 评估思路：基于Software-specific Terms在提示词中的token占比，语句的主谓宾在提示词上下文中是否清晰等给出得分

  2. **清晰度 (Clarity)**: 指令是否易于理解、没有歧义
     - 评估思路：考察提示词中对于同一指代或概念是否存在前后不一致的描述

  3. **结构性 (Structure)**: 内容组织是否逻辑清晰、层次分明
     - 评估思路：基于ICIO、CRISPE、BROKE、CREATE、TAG、RTF、ROSES等prompt框架，判断提示词基于的是哪一框架，并分析是否缺少框架中某些section

  4. **完整性 (Completeness)**: 上下文信息是否完备，是否包含了完成任务所需的所有信息
     - 评估思路：Think step by step，反思提示词是否给出足够信息

  5. **角色定义 (Role Definition)**: 是否明确定义了AI应扮演的角色
     - 评估思路：是否缺少角色定义

  6. **输出格式 (Output Format)**: 是否明确指定了期望的输出格式
     - 评估思路：是否指定输出格式或验证方案

  7. **约束条件 (Constraints)**: 是否设定了适当的限制和边界
     - 评估思路：评估是否指定了代码语言、遵守规范或模式等一些能够缩小解空间的约束

  8. **可执行性 (Actionability)**: AI是否能够根据提示词有效执行任务
     - 评估思路：评估基于该提示词生成的代码体量是否超过2000行，超过该体量的提示词中需要有任务分解与少样本提示

  ## 评估标准
  - **9-10分**: 优秀，该维度表现卓越
  - **7-8分**: 良好，该维度表现较好
  - **5-6分**: 一般，该维度表现中等
  - **3-4分**: 较差，该维度存在明显问题
  - **1-2分**: 很差，该维度存在严重缺陷

  ## 返回格式
  返回必须严格遵循以下JSON结构：
  \`\`\`json
  {
    "originalEvaluation": {
      "scores": {
        "specificity": 分数,
        "clarity": 分数,
        "structure": 分数,
        "completeness": 分数,
        "roleDefinition": 分数,
        "outputFormat": 分数,
        "constraints": 分数,
        "actionability": 分数
      },
      "totalScore": 总分,
      "averageScore": 平均分,
      "strengths": ["优势1", "优势2"],
      "weaknesses": ["不足1", "不足2"]
    },
    "optimizedEvaluation": {
      "scores": {
        "specificity": 分数,
        "clarity": 分数,
        "structure": 分数,
        "completeness": 分数,
        "roleDefinition": 分数,
        "outputFormat": 分数,
        "constraints": 分数,
        "actionability": 分数
      },
      "totalScore": 总分,
      "averageScore": 平均分,
      "strengths": ["优势1", "优势2"],
      "weaknesses": ["不足1", "不足2"]
    },
    "comparison": {
      "winner": "original" | "optimized" | "tie",
      "improvementAreas": ["改进的方面1", "改进的方面2"],
      "recommendation": "基于评估结果的使用建议",
      "keyDifferences": ["主要差异1", "主要差异2"]
    }
  }
  \`\`\`

  ## 重要说明
  1. **客观评估**: 基于专业标准进行客观评估，不偏向任何一方
  2. **具体分析**: 在strengths和weaknesses中提供具体、详细的分析
  3. **实用建议**: 在recommendation中给出实用的使用建议
  4. **只返回JSON**: 不要包含任何JSON之外的解释性文本

  ## 原始提示词
  ${originalPrompt}

  ## 优化后提示词
  ${optimizedPrompt}`;
};

/**
 * 单个Prompt质量评估的模板 - LLM-as-a-Judge 方式
 * @param {string} prompt - 要评估的提示词
 * @param {string} promptType - 提示词类型 ("original" 或 "enhanced")
 * @returns {string} 格式化后的prompt
 */
export const createSinglePromptEvaluationPrompt = (prompt, promptType = "prompt") => {
  return `# Prompt质量评估专家

## 你的角色
你是一位资深的Prompt工程师和AI交互专家，拥有丰富的提示词设计和评估经验。你能够客观、专业地分析提示词的质量。

## 任务概述
我将提供一个${promptType}提示词，你需要从多个维度对这个提示词进行专业评估，并给出客观的质量分析和评分。

## 评估维度
请从以下8个关键维度对提示词进行评估（每个维度1-10分）：

1. **具体性 (Specificity)**: 要求是否具体明确，避免模糊表述
   - 评估思路：基于Software-specific Terms在提示词中的token占比，语句的主谓宾在提示词上下文中是否清晰等给出得分

2. **清晰度 (Clarity)**: 指令是否易于理解、没有歧义
   - 评估思路：考察提示词中对于同一指代或概念是否存在前后不一致的描述

3. **结构性 (Structure)**: 内容组织是否逻辑清晰、层次分明
   - 评估思路：基于以下prompt框架，判断提示词基于的是哪一框架，并分析是否缺少框架中某些section
     * ICIO框架：Input（输入）、Context（上下文）、Instruction（指令）、Output（输出）
     * CRISPE框架：Capacity & Role（AI角色定位）、Insight（问题深度解析）、Steps（实现步骤分解）、Personality（输出风格设定）、Experiment（验证方案）
     * BROKE框架：Background（问题背景）、Request（核心诉求）、Output（输出要求）、Knowledge（相关知识领域）、Envision（潜在优化方向）
     * CREATE框架：Character（角色设定）、Request（核心需求）、Examples（参考示例）、Adjust（调整要求）、Task（具体任务）、Enhance（增强要求）
     * TAG框架：Task（目标任务）、Action（执行动作）、Goal（预期目标）
     * RTF框架：Role（身份定位）、Task（具体任务）、Format（输出格式）
     * ROSES框架：Role（角色定义）、Objective（核心目标）、Scenario（应用场景）、Expected（预期结果）、Steps（实现步骤）

4. **完整性 (Completeness)**: 上下文信息是否完备，是否包含了完成任务所需的所有信息
   - 评估思路：Think step by step，反思提示词是否给出足够信息

5. **角色定义 (Role Definition)**: 是否明确定义了AI应扮演的角色
   - 评估思路：是否缺少角色定义

6. **输出格式 (Output Format)**: 是否明确指定了期望的输出格式
   - 评估思路：是否指定输出格式或验证方案

7. **约束条件 (Constraints)**: 是否设定了适当的限制和边界
   - 评估思路：评估是否指定了代码语言、遵守规范或模式等一些能够缩小解空间的约束

8. **可执行性 (Actionability)**: AI是否能够根据提示词有效执行任务
   - 评估思路：评估基于该提示词生成的代码体量是否超过2000行，超过该体量的提示词中需要有任务分解与少样本提示

## 评估标准
- **9-10分**: 优秀，该维度表现卓越
- **7-8分**: 良好，该维度表现较好
- **5-6分**: 一般，该维度表现中等
- **3-4分**: 较差，该维度存在明显问题
- **1-2分**: 很差，该维度存在严重缺陷

## 返回格式
返回必须严格遵循以下JSON结构：
\`\`\`json
{
  "promptType": "${promptType}",
  "evaluation": {
    "scores": {
      "specificity": 分数(1-10),
      "clarity": 分数(1-10),
      "structure": 分数(1-10),
      "completeness": 分数(1-10),
      "roleDefinition": 分数(1-10),
      "outputFormat": 分数(1-10),
      "constraints": 分数(1-10),
      "actionability": 分数(1-10)
    },
    "totalScore": 总分(8-80),
    "averageScore": 平均分(1-10),
    "normalizedScore": 标准化分数(0-100),
    "strengths": ["优势1", "优势2", "优势3"],
    "weaknesses": ["不足1", "不足2"],
    "improvementSuggestions": ["改进建议1", "改进建议2"],
    "overallAssessment": "整体评价总结"
  }
}
\`\`\`

## 重要说明
1. **客观评估**: 基于专业标准进行客观评估，保持中立立场
2. **具体分析**: 在strengths和weaknesses中提供具体、详细的分析
3. **实用建议**: 在improvementSuggestions中给出具体的改进建议
4. **只返回JSON**: 不要包含任何JSON之外的解释性文本
5. **标准化分数**: normalizedScore = (averageScore - 1) * 100 / 9，转换为0-100分制
6. **评估重点**:
   - 具体性：重点关注专业术语使用和表述明确性
   - 清晰度：检查概念一致性和表述歧义
   - 结构性：识别prompt框架并检查完整性
   - 完整性：逐步分析信息充分性
   - 角色定义：检查是否明确AI角色
   - 输出格式：验证格式规范性
   - 约束条件：评估限制条件的有效性
   - 可执行性：考虑任务复杂度和分解需求

## 待评估的${promptType}提示词
${prompt}`;
};

/**
 * 结构化主观评估模板 - 基于标准评估准则
 * @param {string} prompt - 要评估的提示词
 * @param {string} promptType - 提示词类型 ("original" 或 "enhanced")
 * @returns {string} 格式化后的评估表单
 */
export const createSubjectiveEvaluationTemplate = (prompt, promptType = "prompt") => {
  return `# ${promptType}提示词主观评估表单

## 评估说明
请根据以下标准化准则对${promptType}提示词进行主观评估。每个维度使用1-10分评分制：
- **9-10分**: 优秀 - 完全满足要求，表现卓越
- **7-8分**: 良好 - 基本满足要求，表现较好
- **5-6分**: 一般 - 部分满足要求，有改进空间
- **3-4分**: 较差 - 勉强满足要求，存在明显问题
- **1-2分**: 很差 - 不满足要求，存在严重缺陷

## 评估维度

### 1. 具体性 (Specificity)
**定义**: 要求是否具体明确，避免模糊表述？
**评估要点**:
- Software-specific Terms在提示词中的token占比
- 语句的主谓宾在提示词上下文中是否清晰
- 是否避免了模糊和抽象的表述

### 2. 清晰度 (Clarity)
**定义**: 指令是否易于理解、没有歧义？
**评估要点**:
- 对于同一指代或概念是否存在前后不一致的描述
- 语言表达是否明确无歧义
- 逻辑关系是否清晰

### 3. 结构性 (Structure)
**定义**: 内容组织是否逻辑清晰、层次分明？
**评估要点**:
- 是否遵循了标准的prompt框架（ICIO、CRISPE、BROKE、CREATE、TAG、RTF、ROSES等）
- 框架中的各个section是否完整
- 内容组织是否有逻辑层次

### 4. 完整性 (Completeness)
**定义**: 上下文信息是否完备，是否包含了完成任务所需的所有信息？
**评估要点**:
- Think step by step，反思提示词是否给出足够信息
- 是否包含了完成任务的必要背景信息
- 是否遗漏了关键的上下文信息

### 5. 角色定义 (Role Definition)
**定义**: 是否明确定义了AI应扮演的角色？
**评估要点**:
- 是否明确指定了AI的身份和职责
- 角色定义是否与任务需求匹配
- 是否缺少角色定义

### 6. 输出格式 (Output Format)
**定义**: 是否明确指定了期望的输出格式？
**评估要点**:
- 是否指定了具体的输出格式要求
- 是否包含了验证方案
- 格式要求是否清晰可执行

### 7. 约束条件 (Constraints)
**定义**: 是否设定了适当的限制和边界？
**评估要点**:
- 是否指定了代码语言、遵守规范或模式等约束
- 约束条件是否能够缩小解空间
- 限制条件是否合理有效

### 8. 可执行性 (Actionability)
**定义**: AI是否能够根据提示词有效执行任务？
**评估要点**:
- 基于该提示词生成的代码体量是否合理（不超过2000行）
- 对于复杂任务是否包含了任务分解
- 是否提供了少样本提示或示例

## 待评估的${promptType}提示词
\`\`\`
${prompt}
\`\`\`

## 评估表单

### ${promptType}提示词评分
| 维度 | 评分 (1-10) | 评价说明 |
|------|-------------|----------|
| 具体性 (Specificity) | ___ | |
| 清晰度 (Clarity) | ___ | |
| 结构性 (Structure) | ___ | |
| 完整性 (Completeness) | ___ | |
| 角色定义 (Role Definition) | ___ | |
| 输出格式 (Output Format) | ___ | |
| 约束条件 (Constraints) | ___ | |
| 可执行性 (Actionability) | ___ | |
| **总分** | ___/80 | |
| **平均分** | ___/10 | |
| **标准化分数** | ___/100 | (平均分-1)*100/9 |

## 定性评估

### 主要优势
1. _请列出该提示词的主要优势_
2.
3.

### 主要不足
1. _请列出该提示词的主要不足_
2.
3.

### 改进建议
1. _请提供具体的改进建议_
2.
3.

### 使用场景适配性
_请评估该提示词在不同使用场景下的适用性_

### 整体评价
_请给出对该提示词的整体评价和建议_

---
**评估完成时间**: ___________
**评估者**: ___________`;
};
