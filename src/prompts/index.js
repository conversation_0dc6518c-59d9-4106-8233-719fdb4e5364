/**
 * Prompt管理系统 - 统一管理所有大模型调用的prompt模板
 */

// 导入所有prompt模块
import * as projectStructurePrompts from './projectStructure.js';
import * as promptOptimizationPrompts from './promptOptimization.js';
import * as promptEvaluationPrompts from './promptEvaluation.js';
import * as documentGenerationPrompts from './documentGeneration.js';
import * as treeOutputPrompts from './treeOutput.js';

// 导入统一的LLM调用服务
import { 
  getLLMResponse, 
  getLLMJsonResponse,
  callLLMStream,
  streamJsonResponse 
} from '../services/llmService.js';

/**
 * 统一的prompt调用接口
 * 所有prompt调用都通过这个类进行，确保调用方式的一致性
 */
class PromptManager {
  constructor() {
    this.defaultOptions = {
      temperature: 0.2,
      max_tokens: 4000
    };
  }

  /**
   * 项目结构相关的prompt调用
   */
  async generateProjectStructure(projectType, description, additionalInfo = '', options = {}) {
    const prompt = projectStructurePrompts.createProjectStructurePrompt(projectType, description, additionalInfo);
    return await streamJsonResponse(prompt, { ...this.defaultOptions, ...options });
  }

  async generateStructureFromRepo(projectType, description, repoStructure, additionalInfo = '', options = {}) {
    const prompt = projectStructurePrompts.createStructureFromRepoPrompt(projectType, description, repoStructure, additionalInfo);
    return await streamJsonResponse(prompt, { ...this.defaultOptions, ...options });
  }

  async generateStructureFromFolder(projectType, description, folderStructure, additionalInfo = '', options = {}) {
    const prompt = projectStructurePrompts.createStructureFromFolderPrompt(projectType, description, folderStructure, additionalInfo);
    return await streamJsonResponse(prompt, { ...this.defaultOptions, ...options });
  }

  /**
   * Prompt优化相关的调用
   */
  async optimizePromptAuto(originalPrompt, onStreamChunk = () => {}, options = {}) {
    const prompt = promptOptimizationPrompts.createAutoOptimizePrompt(originalPrompt);
    return await streamJsonResponse(prompt, { ...this.defaultOptions, ...options }, onStreamChunk);
  }

  async optimizePromptAutoVersionB(originalPrompt, onStreamChunk = () => {}, options = {}) {
    const prompt = promptOptimizationPrompts.createAutoOptimizePromptVersionB(originalPrompt);
    return await streamJsonResponse(prompt, { ...this.defaultOptions, ...options }, onStreamChunk);
  }

  async optimizePromptGuided(originalPrompt, guidanceText, onStreamChunk = () => {}, options = {}) {
    const prompt = promptOptimizationPrompts.createGuidedOptimizePrompt(originalPrompt, guidanceText);
    return await streamJsonResponse(prompt, { ...this.defaultOptions, ...options }, onStreamChunk);
  }

  /**
   * 评估单个提示词质量 (LLM-as-a-Judge)
   */
  async evaluateSinglePrompt(prompt, promptType = "prompt", options = {}) {
    const evaluationPrompt = promptEvaluationPrompts.createSinglePromptEvaluationPrompt(prompt, promptType);
    return await getLLMJsonResponse(evaluationPrompt, { ...this.defaultOptions, ...options });
  }

  /**
   * 生成主观评估表单 (用于人工评估)
   */
  generateSubjectiveEvaluationForm(prompt, promptType = "prompt") {
    return promptEvaluationPrompts.createSubjectiveEvaluationTemplate(prompt, promptType);
  }

  /**
   * 综合评估提示词 - 结合LLM-as-a-Judge和结构化评估
   */
  async evaluatePromptComprehensive(prompt, promptType = "prompt", humanScores = null, options = {}) {
    const results = {
      timestamp: new Date().toISOString(),
      prompt,
      promptType,
      evaluationSteps: []
    };

    try {
      // Step 1: LLM-as-a-Judge 评估
      console.log('Step 1: 执行LLM-as-a-Judge评估...');
      results.evaluationSteps.push('LLM-as-a-Judge评估');

      const llmEvaluation = await this.evaluateSinglePrompt(prompt, promptType, options);
      results.llmEvaluation = llmEvaluation;

      // Step 2: 生成主观评估表单 (如果没有提供人工评分)
      if (!humanScores) {
        console.log('Step 2: 生成主观评估表单...');
        results.evaluationSteps.push('生成主观评估表单');
        results.subjectiveEvaluationForm = this.generateSubjectiveEvaluationForm(prompt, promptType);
        results.humanEvaluation = null;
        results.finalScore = llmEvaluation.evaluation.normalizedScore;
      } else {
        // Step 2: 处理人工评估分数
        console.log('Step 2: 处理人工评估分数...');
        results.evaluationSteps.push('处理人工评估分数');
        results.humanEvaluation = this.processHumanScores(humanScores);

        // Step 3: 计算平均分数
        console.log('Step 3: 计算平均分数...');
        results.evaluationSteps.push('计算平均分数');
        results.finalScore = this.calculateAverageScore(
          llmEvaluation.evaluation.normalizedScore,
          results.humanEvaluation.normalizedScore
        );
      }

      // 提供评估指南
      results.evaluationGuidance = {
        workflow: "1. LLM评估 → 2. 人工评估(可选) → 3. 平均分数 → 4. 数据库保存",
        scoringSystem: "1-10分制，转换为0-100标准化分数",
        finalScoreCalculation: humanScores ?
          "最终分数 = (LLM标准化分数 + 人工标准化分数) / 2" :
          "最终分数 = LLM标准化分数"
      };

      return results;
    } catch (error) {
      console.error('综合评估失败:', error);
      throw new Error(`综合评估失败: ${error.message}`);
    }
  }

  /**
   * 处理人工评估分数
   */
  processHumanScores(humanScores) {
    // 期望的humanScores格式:
    // {
    //   accuracy: 8,
    //   relevance: 7,
    //   fluency: 9,
    //   helpfulness: 8,
    //   safety: 10,
    //   humanAlignment: 8
    // }

    const scores = humanScores;
    const totalScore = Object.values(scores).reduce((sum, score) => sum + score, 0);
    const averageScore = totalScore / Object.keys(scores).length;
    const normalizedScore = Math.round((averageScore - 1) * 100 / 9);

    return {
      scores,
      totalScore,
      averageScore: Math.round(averageScore * 100) / 100,
      normalizedScore,
      source: "human"
    };
  }

  /**
   * 计算LLM和人工评估的平均分数
   */
  calculateAverageScore(llmNormalizedScore, humanNormalizedScore) {
    return Math.round((llmNormalizedScore + humanNormalizedScore) / 2);
  }

  /**
   * 文档生成相关的调用
   */
  async generateReadme(projectStructure, projectDescription, options = {}) {
    const prompt = documentGenerationPrompts.createProjectDocumentationPrompt(projectStructure, projectDescription);
    return await getLLMResponse(prompt, { ...this.defaultOptions, ...options });
  }

  async generateDevelopmentGuide(projectStructure, projectDescription, options = {}) {
    const prompt = documentGenerationPrompts.createDevelopmentGuidePrompt(projectStructure, projectDescription);
    return await getLLMResponse(prompt, { ...this.defaultOptions, ...options });
  }

  async generateFileBoilerplate(fileNode, projectStructure, projectDescription, options = {}) {
    const prompt = documentGenerationPrompts.createFileBoilerplatePrompt(fileNode, projectStructure, projectDescription);
    return await getLLMResponse(prompt, { ...this.defaultOptions, ...options });
  }

  /**
   * 树输出增强相关的调用
   */
  async generateProjectDevelopmentPrompt(treeData, asciiTree, capabilities, detectedFiles = [], selectedProjectType = null, options = {}) {
    // 分析项目信息，传递用户选择的项目类型
    const projectInfo = treeOutputPrompts.analyzeProjectInfo(treeData, asciiTree, detectedFiles, selectedProjectType);
    
    // 生成开发prompt
    const prompt = treeOutputPrompts.createProjectDevelopmentPrompt(treeData, asciiTree, capabilities, projectInfo);
    
    return {
      prompt,
      projectInfo
    };
  }

  async enhancePrompt(promptText, onChunk = () => {}, options = {}) {
    const prompt = treeOutputPrompts.createPromptEnhancementPrompt(promptText);
    return await callLLMStream(prompt, { ...this.defaultOptions, ...options }, onChunk);
  }

  async enhancePromptJson(promptText, onProgress = () => {}, options = {}) {
    const prompt = treeOutputPrompts.createPromptEnhancementJsonPrompt(promptText);
    return await streamJsonResponse(prompt, { ...this.defaultOptions, ...options }, onProgress);
  }

  /**
   * 通用调用方法
   */
  async callLLM(prompt, options = {}) {
    return await getLLMResponse(prompt, { ...this.defaultOptions, ...options });
  }

  async callLLMJson(prompt, options = {}) {
    return await getLLMJsonResponse(prompt, { ...this.defaultOptions, ...options });
  }

  async callLLMStream(prompt, onChunk, options = {}) {
    return await callLLMStream(prompt, { ...this.defaultOptions, ...options }, onChunk);
  }

  async callLLMStreamJson(prompt, onProgress, options = {}) {
    return await streamJsonResponse(prompt, { ...this.defaultOptions, ...options }, onProgress);
  }
}

// 导出单例实例
export const promptManager = new PromptManager();

// 导出所有prompt模块，供需要直接访问prompt模板的场景
export {
  projectStructurePrompts,
  promptOptimizationPrompts,
  documentGenerationPrompts,
  treeOutputPrompts
};

// 导出常用的类型和常量
export { PROJECT_TYPES, PROJECT_TYPE_LABELS } from './projectStructure'; 