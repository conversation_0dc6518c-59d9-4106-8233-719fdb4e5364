/**
 * 文档生成服务 - 基于项目结构生成各种文档
 */

import { getLLMResponse } from './llmService';
import { createProjectDocumentationPrompt, createDevelopmentGuidePrompt } from './promptTemplates';

/**
 * 生成项目README.md文档
 * @param {Object} projectStructure - 项目结构对象
 * @param {string} projectDescription - 项目描述
 * @returns {Promise<string>} - 生成的README.md内容
 */
export const generateReadme = async (projectStructure, projectDescription) => {
  const prompt = createProjectDocumentationPrompt(projectStructure, projectDescription);
  return await getLLMResponse(prompt, {
    model: 'gpt-3.5-turbo-0613',
    temperature: 0.7,
    maxTokens: 2048
  });
};

/**
 * 生成项目开发指南
 * @param {Object} projectStructure - 项目结构对象
 * @param {string} projectDescription - 项目描述
 * @returns {Promise<string>} - 生成的开发指南内容
 */
export const generateDevelopmentGuide = async (projectStructure, projectDescription) => {
  const prompt = createDevelopmentGuidePrompt(projectStructure, projectDescription);
  return await getLLMResponse(prompt, {
    model: 'gpt-3.5-turbo-0613',
    temperature: 0.7,
    maxTokens: 2048
  });
};

/**
 * 为特定文件生成样板代码
 * @param {Object} fileNode - 文件节点
 * @param {Object} projectStructure - 整个项目结构
 * @param {string} projectDescription - 项目描述
 * @returns {Promise<string>} - 生成的文件内容
 */
export const generateFileBoilerplate = async (fileNode, projectStructure, projectDescription) => {
  // 构建针对特定文件类型的提示
  let prompt = `请为我生成以下文件的代码内容：
文件名: ${fileNode.name}
功能描述: ${fileNode.capabilities || '未提供'}
项目描述: ${projectDescription}

项目结构概览:
${JSON.stringify(projectStructure, null, 2)}

请为这个文件生成专业、清晰、符合最佳实践的代码。代码应该包含必要的导入、类/函数定义和注释，以实现文件描述的功能。`;

  // 特殊处理不同类型的文件
  if (fileNode.name.endsWith('.py')) {
    prompt += `\n\n请确保代码符合PEP 8规范，包含适当的文档字符串和类型提示。`;
  } else if (fileNode.name.endsWith('.js') || fileNode.name.endsWith('.jsx') || fileNode.name.endsWith('.ts') || fileNode.name.endsWith('.tsx')) {
    prompt += `\n\n请确保代码符合现代JavaScript/TypeScript规范，使用适当的ES6+特性，并包含JSDoc注释。`;
  } else if (fileNode.name === 'README.md') {
    prompt += `\n\n请生成一个全面的Markdown格式README文件，包含项目介绍、安装说明、使用例子和贡献指南等部分。`;
  } else if (fileNode.name === 'requirements.txt' || fileNode.name === 'package.json') {
    prompt += `\n\n请根据项目描述，生成合适的依赖项列表，包含版本号。`;
  }

  return await getLLMResponse(prompt, {
    model: 'gpt-3.5-turbo-0613',
    temperature: 0.7,
    maxTokens: 2048
  });
};

/**
 * 生成项目结构的可视化表示（ASCII树）
 * @param {Object} projectStructure - 项目结构对象
 * @returns {string} - ASCII树表示
 */
export const generateAsciiTree = (projectStructure) => {
  const lines = [];
  
  const buildTree = (node, prefix = '', isLast = true) => {
    // 添加当前节点
    lines.push(`${prefix}${isLast ? '└─ ' : '├─ '}${node.name}${node.type === 'folder' ? '/' : ''}`);
    
    // 计算子节点的前缀
    const childPrefix = prefix + (isLast ? '   ' : '│  ');
    
    // 递归子节点
    if (node.children && node.children.length > 0) {
      node.children.forEach((child, index) => {
        buildTree(child, childPrefix, index === node.children.length - 1);
      });
    }
  };
  
  buildTree(projectStructure);
  return lines.join('\n');
}; 