/**
 * Prompt评估服务 - 处理完整的评估工作流程
 * 包括LLM评估、人工评估、分数计算和数据库保存
 */

import { promptManager } from '../prompts/index.js';
// import { elasticsearchService } from './elasticsearchService.js'; // 浏览器环境不支持
import { localStorageService } from './localStorageService.js';

// ElasticSearch服务连接 (支持模拟和真实连接)
const elasticsearchService = {
  // 模拟模式开关 - 设置为true进行模拟测试，false尝试真实连接
  simulationMode: false,
  simulateSuccess: true, // 在模拟模式下是否模拟连接成功

  // ElasticSearch连接配置
  config: {
    baseUrl: 'http://localhost:9201', // 使用代理服务器
    // 代理服务器会处理身份验证，所以这里不需要用户名密码
  },

  // 创建请求头（代理服务器处理身份验证）
  getAuthHeaders() {
    return {
      'Content-Type': 'application/json'
      // 不需要Authorization头，代理服务器会处理
    };
  },

  async checkConnection() {
    if (this.simulationMode) {
      console.log('🔍 模拟检查ElasticSearch连接...');

      // 模拟网络延迟
      await new Promise(resolve => setTimeout(resolve, 1000));

      if (this.simulateSuccess) {
        console.log('✅ 模拟ElasticSearch连接成功');
        return true;
      } else {
        console.log('❌ 模拟ElasticSearch连接失败');
        return false;
      }
    } else {
      try {
        console.log('🔍 检查真实ElasticSearch连接...');

        // 尝试连接到本地ES实例（使用HTTPS和身份验证）
        const response = await fetch(`${this.config.baseUrl}/_cluster/health`, {
          method: 'GET',
          headers: this.getAuthHeaders(),
          // 在浏览器环境中，我们无法直接控制SSL验证
          // 但可以尝试连接并处理错误
        });

        if (response.ok) {
          const health = await response.json();
          console.log('✅ ElasticSearch连接成功:', health.status);
          return true;
        } else {
          console.log('❌ ElasticSearch响应错误:', response.status, response.statusText);
          return false;
        }
      } catch (error) {
        console.log('❌ ElasticSearch连接失败:', error.message);
        // 如果是CORS或SSL错误，提供更详细的信息
        if (error.message.includes('CORS') || error.message.includes('fetch')) {
          console.log('💡 提示: 可能是CORS策略或SSL证书问题');
          console.log('💡 建议: 在ElasticSearch配置中启用CORS或使用代理服务器');
        }
        return false;
      }
    }
  },

  // 切换模拟模式
  toggleSimulationMode() {
    this.simulationMode = !this.simulationMode;
    console.log(`🔄 切换到${this.simulationMode ? '模拟' : '真实'}模式`);
    return this.simulationMode;
  },

  // 切换模拟连接结果
  toggleSimulationResult() {
    this.simulateSuccess = !this.simulateSuccess;
    console.log(`🔄 模拟连接结果切换为${this.simulateSuccess ? '成功' : '失败'}`);
    return this.simulateSuccess;
  },

  async saveEvaluation(record) {
    if (this.simulationMode) {
      console.log('💾 模拟保存评估结果到ElasticSearch:', record.id);
      await new Promise(resolve => setTimeout(resolve, 500)); // 模拟延迟
      console.log('✅ 模拟保存成功');
      return record;
    } else {
      try {
        console.log('💾 保存评估结果到ElasticSearch:', record.id);

        const response = await fetch(`${this.config.baseUrl}/prompt_evaluations/_doc/${record.id}`, {
          method: 'PUT',
          headers: this.getAuthHeaders(),
          body: JSON.stringify(record)
        });

        if (response.ok) {
          console.log('✅ 评估结果保存成功');
          return record;
        } else {
          throw new Error(`保存失败: ${response.status} ${response.statusText}`);
        }
      } catch (error) {
        console.error('❌ 保存评估结果失败:', error);
        throw error;
      }
    }
  },

  async savePrompt(promptData) {
    if (this.simulationMode) {
      console.log('💾 模拟保存提示词到ElasticSearch:', promptData.id);
      await new Promise(resolve => setTimeout(resolve, 300)); // 模拟延迟
      console.log('✅ 模拟保存成功');
      return promptData;
    } else {
      try {
        console.log('💾 保存提示词到ElasticSearch:', promptData.id);

        const response = await fetch(`${this.config.baseUrl}/prompts/_doc/${promptData.id}`, {
          method: 'PUT',
          headers: this.getAuthHeaders(),
          body: JSON.stringify(promptData)
        });

        if (response.ok) {
          console.log('✅ 提示词保存成功');
          return promptData;
        } else {
          throw new Error(`保存失败: ${response.status} ${response.statusText}`);
        }
      } catch (error) {
        console.error('❌ 保存提示词失败:', error);
        throw error;
      }
    }
  },

  async getEvaluationStatistics() {
    if (this.simulationMode) {
      console.log('📊 模拟从ElasticSearch获取统计信息');
      await new Promise(resolve => setTimeout(resolve, 800)); // 模拟延迟

      // 返回模拟的统计数据
      return {
        totalEvaluations: 15,
        averageOriginalScore: 72.5,
        averageEnhancedScore: 85.3,
        averageImprovement: 12.8,
        improvementLevels: [
          { key: '显著改进', doc_count: 5 },
          { key: '明显改进', doc_count: 7 },
          { key: '轻微改进', doc_count: 3 }
        ],
        scoreDistribution: [
          { range: '60-70', count: 3 },
          { range: '70-80', count: 8 },
          { range: '80-90', count: 4 }
        ]
      };
    } else {
      try {
        console.log('📊 从ElasticSearch获取统计信息');

        const response = await fetch(`${this.config.baseUrl}/prompt_evaluations/_search`, {
          method: 'POST',
          headers: this.getAuthHeaders(),
          body: JSON.stringify({
            size: 0,
            aggs: {
              total_evaluations: { value_count: { field: '_id' } },
              avg_original_score: { avg: { field: 'originalPrompt.score' } },
              avg_enhanced_score: { avg: { field: 'enhancedPrompt.score' } },
              avg_improvement: { avg: { field: 'improvement.absoluteImprovement' } }
            }
          })
        });

        if (response.ok) {
          const result = await response.json();
          const aggs = result.aggregations;

          return {
            totalEvaluations: aggs.total_evaluations.value || 0,
            averageOriginalScore: aggs.avg_original_score.value || 0,
            averageEnhancedScore: aggs.avg_enhanced_score.value || 0,
            averageImprovement: aggs.avg_improvement.value || 0,
            improvementLevels: [],
            scoreDistribution: []
          };
        } else {
          throw new Error(`查询失败: ${response.status} ${response.statusText}`);
        }
      } catch (error) {
        console.error('❌ 获取统计信息失败:', error);
        return {
          totalEvaluations: 0,
          averageOriginalScore: 0,
          averageEnhancedScore: 0,
          averageImprovement: 0,
          improvementLevels: [],
          scoreDistribution: []
        };
      }
    }
  },

  async getAllEvaluations() {
    if (this.simulationMode) {
      console.log('📊 模拟从ElasticSearch获取所有评估数据');
      await new Promise(resolve => setTimeout(resolve, 500)); // 模拟延迟
      return [];
    } else {
      try {
        console.log('📊 从ElasticSearch获取所有评估数据');

        const response = await fetch(`${this.config.baseUrl}/prompt_evaluations/_search`, {
          method: 'POST',
          headers: this.getAuthHeaders(),
          body: JSON.stringify({
            size: 1000, // 获取最多1000条记录
            sort: [{ timestamp: { order: 'desc' } }]
          })
        });

        if (response.ok) {
          const data = await response.json();
          return data.hits.hits.map(hit => ({
            id: hit._id,
            ...hit._source
          }));
        } else {
          throw new Error(`获取评估数据失败: ${response.status} ${response.statusText}`);
        }
      } catch (error) {
        console.error('❌ 从ElasticSearch获取评估数据失败:', error);
        throw error;
      }
    }
  },

  async getAllPrompts() {
    if (this.simulationMode) {
      console.log('📊 模拟从ElasticSearch获取所有提示词数据');
      await new Promise(resolve => setTimeout(resolve, 500)); // 模拟延迟
      return [];
    } else {
      try {
        console.log('📊 从ElasticSearch获取所有提示词数据');

        const response = await fetch(`${this.config.baseUrl}/prompts/_search`, {
          method: 'POST',
          headers: this.getAuthHeaders(),
          body: JSON.stringify({
            size: 1000, // 获取最多1000条记录
            sort: [{ created_at: { order: 'desc' } }]
          })
        });

        if (response.ok) {
          const data = await response.json();
          return data.hits.hits.map(hit => ({
            id: hit._id,
            ...hit._source
          }));
        } else {
          throw new Error(`获取提示词数据失败: ${response.status} ${response.statusText}`);
        }
      } catch (error) {
        console.error('❌ 从ElasticSearch获取提示词数据失败:', error);
        throw error;
      }
    }
  }
};

/**
 * Prompt评估工作流程服务
 */
class PromptEvaluationService {
  constructor() {
    // ElasticSearch可用性状态 (将在初始化时检查)
    this.useElasticsearch = false;
    this.elasticsearchChecked = false;

    // 备用的内存存储 (当ElasticSearch不可用时)
    this.database = {
      evaluations: [],
      prompts: []
    };

    // 初始化时检查ElasticSearch可用性
    this.initializeStorageBackend().then(isAvailable => {
      console.log(`🔧 PromptEvaluationService 初始化完成，ES可用性: ${isAvailable}`);
    }).catch(error => {
      console.warn('🔧 PromptEvaluationService 初始化失败:', error);
    });
  }

  /**
   * 初始化存储后端 - 检查ElasticSearch可用性
   */
  async initializeStorageBackend() {
    if (this.elasticsearchChecked) {
      return this.useElasticsearch;
    }

    try {
      console.log('🔍 检查ElasticSearch连接状态...');
      const isESAvailable = await elasticsearchService.checkConnection();

      if (isESAvailable) {
        console.log('✅ ElasticSearch连接成功，将使用ES作为主要存储');
        this.useElasticsearch = true;
      } else {
        console.log('⚠️ ElasticSearch连接失败，将使用本地存储');
        this.useElasticsearch = false;
      }
    } catch (error) {
      console.warn('❌ ElasticSearch检查失败，将使用本地存储:', error.message);
      this.useElasticsearch = false;
    }

    this.elasticsearchChecked = true;
    return this.useElasticsearch;
  }

  /**
   * 完整的评估工作流程
   * @param {string} originalPrompt - 原始提示词
   * @param {string} enhancedPrompt - 增强后的提示词
   * @param {Object} humanScores - 人工评估分数 (可选)
   * @param {Object} options - 评估选项
   * @returns {Promise<Object>} 评估结果
   */
  async evaluatePromptWorkflow(originalPrompt, enhancedPrompt, humanScores = null, options = {}) {
    console.log('开始Prompt评估工作流程...');
    
    const workflowResult = {
      workflowId: this.generateWorkflowId(),
      timestamp: new Date().toISOString(),
      originalPrompt,
      enhancedPrompt,
      steps: []
    };

    try {
      // Step 1: 评估原始提示词
      console.log('Step 1: 评估原始提示词...');
      workflowResult.steps.push('评估原始提示词');
      
      const originalEvaluation = await promptManager.evaluatePromptComprehensive(
        originalPrompt,
        "原始",
        humanScores?.original,
        options
      );
      workflowResult.originalEvaluation = originalEvaluation;

      // Step 2: 评估增强提示词
      console.log('Step 2: 评估增强提示词...');
      workflowResult.steps.push('评估增强提示词');
      
      const enhancedEvaluation = await promptManager.evaluatePromptComprehensive(
        enhancedPrompt,
        "增强",
        humanScores?.enhanced,
        options
      );
      workflowResult.enhancedEvaluation = enhancedEvaluation;

      // Step 3: 计算改进度
      console.log('Step 3: 计算改进度...');
      workflowResult.steps.push('计算改进度');
      
      workflowResult.improvement = this.calculateImprovement(
        originalEvaluation.finalScore,
        enhancedEvaluation.finalScore
      );

      // Step 4: 保存到数据库
      console.log('Step 4: 保存评估结果到数据库...');
      workflowResult.steps.push('保存到数据库');
      
      const savedData = await this.saveEvaluationToDatabase(workflowResult);
      workflowResult.databaseId = savedData.id;

      // Step 5: 生成评估报告
      console.log('Step 5: 生成评估报告...');
      workflowResult.steps.push('生成评估报告');
      
      workflowResult.report = this.generateEvaluationReport(workflowResult);

      console.log('评估工作流程完成!');
      return workflowResult;

    } catch (error) {
      console.error('评估工作流程失败:', error);
      workflowResult.error = error.message;
      throw error;
    }
  }

  /**
   * 计算改进度
   */
  calculateImprovement(originalScore, enhancedScore) {
    const absoluteImprovement = enhancedScore - originalScore;
    const relativeImprovement = originalScore > 0 ? 
      Math.round((absoluteImprovement / originalScore) * 100) : 0;

    return {
      absoluteImprovement: Math.round(absoluteImprovement * 100) / 100,
      relativeImprovement,
      improvementLevel: this.getImprovementLevel(relativeImprovement),
      summary: this.getImprovementSummary(absoluteImprovement, relativeImprovement)
    };
  }

  /**
   * 获取改进等级
   */
  getImprovementLevel(relativeImprovement) {
    if (relativeImprovement >= 50) return "显著改进";
    if (relativeImprovement >= 20) return "明显改进";
    if (relativeImprovement >= 10) return "轻微改进";
    if (relativeImprovement >= 0) return "基本持平";
    if (relativeImprovement >= -10) return "轻微下降";
    if (relativeImprovement >= -20) return "明显下降";
    return "显著下降";
  }

  /**
   * 获取改进总结
   */
  getImprovementSummary(absoluteImprovement, relativeImprovement) {
    if (absoluteImprovement > 0) {
      return `增强后的提示词比原始提示词提高了${Math.abs(absoluteImprovement)}分 (${relativeImprovement}%)`;
    } else if (absoluteImprovement < 0) {
      return `增强后的提示词比原始提示词降低了${Math.abs(absoluteImprovement)}分 (${Math.abs(relativeImprovement)}%)`;
    } else {
      return "增强后的提示词与原始提示词评分相同";
    }
  }

  /**
   * 保存评估结果到数据库
   */
  async saveEvaluationToDatabase(evaluationResult) {
    // 确保存储后端已初始化
    await this.initializeStorageBackend();

    const record = {
      id: this.generateDatabaseId(),
      workflowId: evaluationResult.workflowId,
      timestamp: evaluationResult.timestamp,
      originalPrompt: {
        content: evaluationResult.originalPrompt,
        score: evaluationResult.originalEvaluation.finalScore,
        evaluation: evaluationResult.originalEvaluation
      },
      enhancedPrompt: {
        content: evaluationResult.enhancedPrompt,
        score: evaluationResult.enhancedEvaluation.finalScore,
        evaluation: evaluationResult.enhancedEvaluation
      },
      improvement: evaluationResult.improvement,
      metadata: {
        evaluationMethod: "LLM-as-a-Judge + Human (optional)",
        scoringSystem: "0-100 normalized scale",
        version: "1.0"
      }
    };

    // 优先尝试保存到ElasticSearch
    if (this.useElasticsearch) {
      try {
        console.log('💾 保存评估结果到ElasticSearch...');

        // 保存评估结果
        await elasticsearchService.saveEvaluation(record);

        // 保存原始提示词
        await elasticsearchService.savePrompt({
          id: this.generateDatabaseId(),
          content: evaluationResult.originalPrompt,
          type: "original",
          score: evaluationResult.originalEvaluation.finalScore,
          evaluationId: record.id
        });

        // 保存增强提示词
        await elasticsearchService.savePrompt({
          id: this.generateDatabaseId(),
          content: evaluationResult.enhancedPrompt,
          type: "enhanced",
          score: evaluationResult.enhancedEvaluation.finalScore,
          evaluationId: record.id
        });

        console.log(`✅ 评估结果已保存到ElasticSearch，记录ID: ${record.id}`);
        return record;
      } catch (error) {
        console.warn('❌ ElasticSearch保存失败，降级到本地存储:', error.message);
        // 如果ES保存失败，将标记为不可用并继续使用本地存储
        this.useElasticsearch = false;
      }
    }

    // 尝试保存到本地存储
    try {
      console.log('🔍 开始保存评估结果到本地存储...');
      console.log('📊 评估记录:', record);

      const isLocalConnected = await localStorageService.checkConnection();
      console.log('🔗 本地存储连接状态:', isLocalConnected);

      if (isLocalConnected) {
        // 保存评估结果
        console.log('💾 保存评估结果...');
        await localStorageService.saveEvaluation(record);

        // 保存原始提示词
        console.log('💾 保存原始提示词...');
        await localStorageService.savePrompt({
          id: this.generateDatabaseId(),
          content: evaluationResult.originalPrompt,
          type: "original",
          score: evaluationResult.originalEvaluation.finalScore,
          evaluationId: record.id
        });

        // 保存增强提示词
        console.log('💾 保存增强提示词...');
        await localStorageService.savePrompt({
          id: this.generateDatabaseId(),
          content: evaluationResult.enhancedPrompt,
          type: "enhanced",
          score: evaluationResult.enhancedEvaluation.finalScore,
          evaluationId: record.id
        });

        console.log(`✅ 评估结果已保存到本地存储，记录ID: ${record.id}`);

        // 验证数据是否保存成功
        const exportedData = await localStorageService.exportData();
        console.log('📋 当前存储的评估数据数量:', exportedData.evaluations.length);

        return record;
      }
    } catch (error) {
      console.error('❌ 本地存储保存失败，使用内存存储:', error);
      console.error('错误详情:', error.stack);
    }

    // 备用：保存到内存数据库
    this.database.evaluations.push(record);
    this.database.prompts.push({
      id: this.generateDatabaseId(),
      content: evaluationResult.originalPrompt,
      type: "original",
      score: evaluationResult.originalEvaluation.finalScore,
      evaluationId: record.id
    });
    this.database.prompts.push({
      id: this.generateDatabaseId(),
      content: evaluationResult.enhancedPrompt,
      type: "enhanced",
      score: evaluationResult.enhancedEvaluation.finalScore,
      evaluationId: record.id
    });

    console.log(`评估结果已保存到内存数据库，记录ID: ${record.id}`);
    return record;
  }

  /**
   * 生成评估报告
   */
  generateEvaluationReport(evaluationResult) {
    const original = evaluationResult.originalEvaluation;
    const enhanced = evaluationResult.enhancedEvaluation;
    const improvement = evaluationResult.improvement;

    return {
      summary: {
        originalScore: original.finalScore,
        enhancedScore: enhanced.finalScore,
        improvement: improvement.absoluteImprovement,
        improvementLevel: improvement.improvementLevel
      },
      recommendations: this.generateRecommendations(evaluationResult),
      nextSteps: this.generateNextSteps(evaluationResult),
      detailedAnalysis: {
        strengths: this.analyzeStrengths(original, enhanced),
        improvements: this.analyzeImprovements(original, enhanced),
        concerns: this.analyzeConcerns(original, enhanced)
      }
    };
  }

  /**
   * 生成建议
   */
  generateRecommendations(evaluationResult) {
    const improvement = evaluationResult.improvement;
    const enhancedScore = evaluationResult.enhancedEvaluation.finalScore;

    const recommendations = [];

    if (improvement.absoluteImprovement > 0) {
      recommendations.push("建议使用增强后的提示词，质量有明显提升");
    } else if (improvement.absoluteImprovement < 0) {
      recommendations.push("建议重新优化提示词，当前增强版本质量有所下降");
    } else {
      recommendations.push("两个版本质量相当，可根据具体使用场景选择");
    }

    if (enhancedScore >= 80) {
      recommendations.push("提示词质量优秀，可以投入生产使用");
    } else if (enhancedScore >= 60) {
      recommendations.push("提示词质量良好，建议进一步优化后使用");
    } else {
      recommendations.push("提示词质量需要改进，建议重新设计");
    }

    return recommendations;
  }

  /**
   * 生成下一步行动建议
   */
  generateNextSteps(evaluationResult) {
    const enhancedScore = evaluationResult.enhancedEvaluation.finalScore;
    const hasHumanEvaluation = evaluationResult.enhancedEvaluation.humanEvaluation !== null;

    const nextSteps = [];

    if (!hasHumanEvaluation) {
      nextSteps.push("进行人工评估以获得更准确的质量评分");
    }

    if (enhancedScore < 70) {
      nextSteps.push("基于评估反馈进一步优化提示词");
      nextSteps.push("考虑使用不同的优化策略或方法");
    }

    if (enhancedScore >= 70) {
      nextSteps.push("在小范围内测试提示词的实际效果");
      nextSteps.push("收集用户反馈并持续改进");
    }

    nextSteps.push("定期重新评估提示词质量");

    return nextSteps;
  }

  /**
   * 分析优势
   */
  analyzeStrengths(original, enhanced) {
    // 简化的分析逻辑
    const strengths = [];
    
    if (enhanced.finalScore > original.finalScore) {
      strengths.push("增强版本在整体质量上有所提升");
    }
    
    return strengths;
  }

  /**
   * 分析改进点
   */
  analyzeImprovements(original, enhanced) {
    // 简化的分析逻辑
    return ["基于LLM评估的改进建议"];
  }

  /**
   * 分析关注点
   */
  analyzeConcerns(original, enhanced) {
    // 简化的分析逻辑
    const concerns = [];
    
    if (enhanced.finalScore < original.finalScore) {
      concerns.push("增强版本的质量低于原始版本");
    }
    
    return concerns;
  }

  /**
   * 生成工作流程ID
   */
  generateWorkflowId() {
    return `workflow_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * 生成数据库ID
   */
  generateDatabaseId() {
    return `db_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * 获取评估历史
   */
  getEvaluationHistory(limit = 10) {
    return this.database.evaluations
      .sort((a, b) => new Date(b.timestamp) - new Date(a.timestamp))
      .slice(0, limit);
  }

  /**
   * 手动检查并切换存储后端
   */
  async checkAndSwitchStorageBackend() {
    console.log('🔄 手动检查存储后端状态...');
    this.elasticsearchChecked = false; // 重置检查状态
    return await this.initializeStorageBackend();
  }

  /**
   * 获取当前存储后端状态
   */
  getStorageBackendStatus() {
    return {
      useElasticsearch: this.useElasticsearch,
      elasticsearchChecked: this.elasticsearchChecked,
      currentBackend: this.useElasticsearch ? 'ElasticSearch' : 'LocalStorage',
      simulationMode: elasticsearchService.simulationMode,
      simulateSuccess: elasticsearchService.simulateSuccess
    };
  }

  /**
   * 切换模拟连接结果（用于测试）
   */
  toggleSimulationResult() {
    return elasticsearchService.toggleSimulationResult();
  }

  /**
   * 切换模拟模式
   */
  toggleSimulationMode() {
    return elasticsearchService.toggleSimulationMode();
  }

  /**
   * 获取提示词统计
   */
  async getPromptStatistics() {
    console.log('📊 获取提示词统计信息...');

    // 确保存储后端已初始化
    await this.initializeStorageBackend();

    // 优先尝试从ElasticSearch获取统计信息
    if (this.useElasticsearch) {
      try {
        console.log('📊 从ElasticSearch获取统计信息...');
        const stats = await elasticsearchService.getEvaluationStatistics();

        if (stats && stats.totalEvaluations > 0) {
          console.log('✅ 成功从ElasticSearch获取统计信息');
          return {
            totalEvaluations: stats.totalEvaluations,
            averageOriginalScore: stats.averageOriginalScore,
            averageEnhancedScore: stats.averageEnhancedScore,
            averageImprovement: stats.averageImprovement,
            improvementRate: stats.improvementLevels
              .filter(level => level.key.includes('改进'))
              .reduce((sum, level) => sum + level.doc_count, 0) / stats.totalEvaluations * 100,
            improvementLevels: stats.improvementLevels,
            scoreDistribution: stats.scoreDistribution,
            dataSource: 'ElasticSearch'
          };
        }
      } catch (error) {
        console.warn('❌ 从ElasticSearch获取统计信息失败，降级到本地存储:', error.message);
        this.useElasticsearch = false;
      }
    }

    // 尝试从本地存储获取统计信息
    try {
      console.log('📊 从本地存储获取统计信息...');
      const isLocalConnected = await localStorageService.checkConnection();

      if (isLocalConnected) {
        const stats = await localStorageService.getEvaluationStatistics();

        if (stats.totalEvaluations > 0) {
          console.log('✅ 成功从本地存储获取统计信息');
          return {
            totalEvaluations: stats.totalEvaluations,
            averageOriginalScore: stats.averageOriginalScore,
            averageEnhancedScore: stats.averageEnhancedScore,
            averageImprovement: stats.averageImprovement,
            improvementRate: stats.improvementLevels
              .filter(level => level.key.includes('改进'))
              .reduce((sum, level) => sum + level.doc_count, 0) / stats.totalEvaluations * 100,
            improvementLevels: stats.improvementLevels,
            scoreDistribution: stats.scoreDistribution,
            dataSource: 'LocalStorage'
          };
        }
      }
    } catch (error) {
      console.warn('❌ 从本地存储获取统计信息失败，使用内存数据:', error.message);
    }

    // 备用：从内存数据库获取统计信息
    console.log('📊 从内存数据库获取统计信息...');
    const evaluations = this.database.evaluations;

    if (evaluations.length === 0) {
      return {
        message: "暂无评估数据",
        dataSource: 'Memory'
      };
    }

    const originalScores = evaluations.map(e => e.originalPrompt.score);
    const enhancedScores = evaluations.map(e => e.enhancedPrompt.score);

    console.log('✅ 成功从内存数据库获取统计信息');
    return {
      totalEvaluations: evaluations.length,
      averageOriginalScore: this.calculateAverage(originalScores),
      averageEnhancedScore: this.calculateAverage(enhancedScores),
      averageImprovement: this.calculateAverage(evaluations.map(e => e.improvement.absoluteImprovement)),
      improvementRate: evaluations.filter(e => e.improvement.absoluteImprovement > 0).length / evaluations.length * 100,
      dataSource: 'Memory'
    };
  }

  /**
   * 计算平均值
   */
  calculateAverage(numbers) {
    if (numbers.length === 0) return 0;
    return Math.round(numbers.reduce((sum, num) => sum + num, 0) / numbers.length * 100) / 100;
  }

  /**
   * 获取所有评估数据
   */
  async getAllEvaluations() {
    // 确保存储后端已初始化
    await this.initializeStorageBackend();

    // 优先尝试从ElasticSearch获取数据
    if (this.useElasticsearch) {
      try {
        console.log('📊 从ElasticSearch获取所有评估数据...');
        return await elasticsearchService.getAllEvaluations();
      } catch (error) {
        console.warn('❌ 从ElasticSearch获取评估数据失败，降级到本地存储:', error.message);
        this.useElasticsearch = false;
      }
    }

    // 降级到本地存储
    try {
      console.log('📊 从本地存储获取评估数据...');
      const isLocalConnected = await localStorageService.checkConnection();

      if (isLocalConnected) {
        return await localStorageService.getLLMEvaluations();
      }
    } catch (error) {
      console.warn('❌ 从本地存储获取评估数据失败，使用内存数据:', error.message);
    }

    // 最后降级到内存数据
    return this.database.evaluations;
  }

  /**
   * 获取所有提示词数据
   */
  async getAllPrompts() {
    // 确保存储后端已初始化
    await this.initializeStorageBackend();

    // 优先尝试从ElasticSearch获取数据
    if (this.useElasticsearch) {
      try {
        console.log('📊 从ElasticSearch获取所有提示词数据...');
        return await elasticsearchService.getAllPrompts();
      } catch (error) {
        console.warn('❌ 从ElasticSearch获取提示词数据失败，降级到本地存储:', error.message);
        this.useElasticsearch = false;
      }
    }

    // 降级到本地存储
    try {
      console.log('📊 从本地存储获取提示词数据...');
      const isLocalConnected = await localStorageService.checkConnection();

      if (isLocalConnected) {
        const result = await localStorageService.searchPrompts('');
        return result.prompts || [];
      }
    } catch (error) {
      console.warn('❌ 从本地存储获取提示词数据失败，使用内存数据:', error.message);
    }

    // 最后降级到内存数据
    return this.database.prompts;
  }
}

// 导出单例实例
export const promptEvaluationService = new PromptEvaluationService();

export default PromptEvaluationService;
