/**
 * LLM服务 - 封装大模型API调用
 */

// API端点
const API_ENDPOINT = 'https://aigc.sankuai.com/v1/openai/native/chat/completions';
// API密钥
const API_KEY = '1696806567389331530';

/**
 * 调用大模型API
 * @param {string} prompt - 提示文本
 * @param {Object} options - 配置选项
 * @param {string} options.model - 模型名称，默认为gpt-3.5-turbo
 * @param {boolean} options.stream - 是否使用流式响应，默认为false
 * @param {number} options.maxTokens - 最大生成token数量
 * @param {number} options.temperature - 温度参数，控制随机性
 * @returns {Promise<Object>} - 返回API响应数据
 */
const callLLM = async (prompt, options = {}) => {
  const {
    model = 'qwen-max-latest',
    stream = false,
    temperature = 0.2,
    max_tokens = 8000, 
    // 支持更多可能的参数
    ...otherOptions
  } = options;

  console.log(`调用LLM API: 模型=${model}, 提示长度=${prompt.length}, 流式=${stream}, 最大生成长度=${max_tokens}`);

  try {
    const requestBody = {
      model,
      messages: [{ role: 'user', content: prompt }],
      stream,
      temperature,
      max_tokens,
      ...otherOptions  // 支持传递其他任何额外参数
    };

    const response = await fetch(API_ENDPOINT, {
      method: 'POST',
      headers: {
        'Authorization': API_KEY,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(requestBody)
    });

    if (!response.ok) {
      const errorText = await response.text();
      throw new Error(`API请求失败: ${response.status} ${response.statusText} - ${errorText}`);
    }

    if (stream) {
      return response; // 直接返回响应对象，用于后续流式处理
    } else {
      const data = await response.json();
      console.log(`LLM API调用成功: 响应长度=${JSON.stringify(data).length}`);
      return data;
    }
  } catch (error) {
    console.error('LLM API调用失败:', error);
    throw error;
  }
};

/**
 * 调用大模型API并处理流式响应
 * @param {string} prompt - 提示文本
 * @param {Object} options - 配置选项
 * @param {Function} onChunk - 处理每个数据块的回调函数
 * @returns {Promise<string>} - 返回完整的响应文本
 */
const callLLMStream = async (prompt, options = {}, onChunk = () => {}) => {
  try {
    // 确保流式选项开启
    const streamOptions = { 
      ...options, 
      stream: true,
      max_tokens: options.max_tokens || 4000  // 默认较大的token限制
    };
    
    // 获取响应流
    const response = await callLLM(prompt, streamOptions);
    
    if (!response.body) {
      throw new Error('流式响应不可用');
    }
    
    // 创建读取器
    const reader = response.body.getReader();
    const decoder = new TextDecoder();
    let fullText = '';
    let buffer = '';
    let lastChunkTime = Date.now();
    let isFinished = false;
    let noDataCounter = 0;
    const MAX_NO_DATA_COUNT = 10; // 允许最多10次无数据读取
    
    // 处理数据流
    while (!isFinished) {
      let timeoutPromise = new Promise((_, reject) => {
        setTimeout(() => reject(new Error('读取流超时')), 15000); // 15秒超时
      });
      
      let readPromise = reader.read();
      
      let result;
      try {
        result = await Promise.race([readPromise, timeoutPromise]);
      } catch (error) {
        console.warn('流读取超时，检查已获取内容是否完整');
        // 如果内容看起来足够完整，就可以结束
        if (fullText.length > 500) {
          isFinished = true;
          break;
        } else {
          throw new Error('流读取超时且内容不完整');
        }
      }
      
      const { done, value } = result;
      
      if (done) {
        isFinished = true;
        // 最后检查一下buffer是否有剩余内容
        if (buffer.trim()) {
          try {
            const jsonStr = buffer.startsWith('data: ') ? buffer.slice(5) : buffer;
            const data = JSON.parse(jsonStr);
            const content = data.choices?.[0]?.delta?.content || 
                          data.choices?.[0]?.message?.content || '';
            if (content) {
              fullText += content;
              onChunk(content, fullText);
            }
          } catch (e) {
            // 忽略最后可能的解析错误
          }
        }
        break;
      }
      
      // 如果获取到空数据，计数器+1
      if (!value || value.length === 0) {
        noDataCounter++;
        if (noDataCounter >= MAX_NO_DATA_COUNT) {
          console.warn('连续多次获取空数据，可能已完成');
          isFinished = true;
          break;
        }
        // 等待一小段时间再继续
        await new Promise(resolve => setTimeout(resolve, 500));
        continue;
      } else {
        // 有数据时重置计数器
        noDataCounter = 0;
      }
      
      // 检测是否长时间没有接收到新数据
      const currentTime = Date.now();
      if (currentTime - lastChunkTime > 10000) { // 10秒超时
        console.warn('流式处理接收数据超时，当前已获取内容长度:', fullText.length);
        // 继续处理，不终止，给API更多时间
      }
      lastChunkTime = currentTime;
      
      // 解码本次收到的数据
      const chunk = decoder.decode(value, { stream: true });
      buffer += chunk;
      
      // 分割数据行
      const lines = buffer.split('\n');
      buffer = lines.pop() || ''; // 将最后一个可能不完整的行保留在缓冲区
      
      for (const line of lines) {
        // 忽略空行和结束标记
        if (!line.trim() || line.trim() === 'data: [DONE]') continue;
        
        // 处理数据行
        try {
          // 处理标准SSE格式: "data: {...}"
          const jsonStr = line.startsWith('data: ') ? line.slice(5) : line;
          const data = JSON.parse(jsonStr);
          
          // 提取内容
          const content = data.choices?.[0]?.delta?.content || 
                        data.choices?.[0]?.message?.content || '';
          
          if (content) {
            fullText += content;
            onChunk(content, fullText);
          }
        } catch (e) {
          console.warn('解析流式数据出错:', line, e);
        }
      }
    }
    
    console.log(`流式响应完成: 总长度=${fullText.length}`);
    return fullText;
  } catch (error) {
    console.error('流式请求失败:', error);
    throw error;
  }
};

/**
 * 流式处理JSON响应
 * @param {string} prompt - 提示文本
 * @param {Object} options - 配置选项
 * @param {Function} onProgress - 进度回调，接收当前文本和处理状态
 * @returns {Promise<Object>} - 返回最终解析的JSON对象
 */
const streamJsonResponse = async (prompt, options = {}, onProgress = () => {}) => {
  let parsingAttempts = 0;
  let finalJson = null;
  let lastProgressUpdate = Date.now();
  let jsonParseStarted = false;
  let failedParseAttempts = 0;
  const MAX_FAILED_PARSE_ATTEMPTS = 5; // 最多允许5次解析失败
  let fullText = ''; // 初始化 fullText 变量，避免在 catch 块中未定义
  
  // 确保使用较大的max_tokens
  const enhancedOptions = {
    ...options,
    max_tokens: options.max_tokens || 4000
  };
  
  // 计算进度百分比 - 更平滑的进度计算
  const calculateProgress = (text, status) => {
    // 动态估计响应长度上限 - 基于输入长度动态调整
    const inputLength = prompt.length;
    const estimatedMaxLength = Math.max(10000, inputLength * 1.5); // 至少10000字符
    
    const lengthBasedProgress = Math.min(90, Math.ceil((text.length / estimatedMaxLength) * 100));
    
    // 根据状态调整进度
    switch(status) {
      case 'parsing':
        // 解析阶段，进度为90-99%
        return Math.min(99, Math.max(90, lengthBasedProgress + 5));
      case 'complete':
        return 100;
      case 'error':
        return Math.min(80, lengthBasedProgress); // 错误状态下保守显示进度
      default: // generating
        // 生成阶段，进度为0-90%
        return lengthBasedProgress;
    }
  };
  
  // 状态更新函数
  const updateProgress = (text, status) => {
    // 限制更新频率，避免UI频繁刷新
    const now = Date.now();
    if (now - lastProgressUpdate < 100 && status === 'generating') {
      return;
    }
    lastProgressUpdate = now;
    
    // 计算进度
    const progress = calculateProgress(text, status);
    
    onProgress(text, {
      status,
      progress,
      json: finalJson
    });
  };
  
  // 提前设置初始进度
  updateProgress('', 'generating');
  
  try {
    // 收集完整响应
    fullText = await callLLMStream(
      prompt, 
      enhancedOptions,
      (chunk, currentText) => {
        // 每收到新块，就尝试更新进度
        let status = 'generating';
        
        // 检测是否开始输出JSON内容
        if (!jsonParseStarted && (
          currentText.includes('```json') || 
          currentText.includes('{') || 
          currentText.includes('[')
        )) {
          jsonParseStarted = true;
        }
        
        // 如果已经开始输出JSON，周期性尝试解析
        if (jsonParseStarted && 
            currentText.length > 100 && 
            (parsingAttempts === 0 || currentText.length - parsingAttempts * 200 > 200)) {
          parsingAttempts = Math.floor(currentText.length / 200);
          try {
            const maybeJson = extractJsonFromText(currentText);
            if (maybeJson) {
              finalJson = maybeJson;
              status = 'parsing';
              failedParseAttempts = 0; // 成功解析后重置失败计数
            } else {
              failedParseAttempts++;
            }
          } catch (e) {
            failedParseAttempts++;
            // 静默失败，继续收集更多数据
          }
          
          // 如果多次解析失败且内容已经很长，检查是否包含完整JSON结构
          if (failedParseAttempts > MAX_FAILED_PARSE_ATTEMPTS && currentText.length > 5000) {
            // 尝试查找最后一个完整的JSON结构
            try {
              // 查找大括号配对
              let openCount = 0;
              let closeCount = 0;
              let lastOpenPos = -1;
              let lastClosePos = -1;
              
              for (let i = 0; i < currentText.length; i++) {
                if (currentText[i] === '{') {
                  openCount++;
                  lastOpenPos = i;
                } else if (currentText[i] === '}') {
                  closeCount++;
                  lastClosePos = i;
                  
                  // 如果找到配对的大括号，尝试解析
                  if (openCount === closeCount && openCount > 0) {
                    try {
                      const jsonSubstring = currentText.substring(0, i + 1);
                      const extractedJson = extractJsonFromText(jsonSubstring);
                      if (extractedJson) {
                        finalJson = extractedJson;
                        status = 'parsing';
                        break;
                      }
                    } catch (e) {
                      // 忽略局部解析错误
                    }
                  }
                }
              }
            } catch (e) {
              // 忽略额外的解析尝试错误
            }
          }
        }
        
        updateProgress(currentText, status);
      }
    );
    
    // 尝试从最终文本中解析JSON
    updateProgress(fullText, 'parsing');
    
    // 多次尝试不同的提取方法
    const extractionMethods = [
      // 标准JSON提取
      () => extractJsonFromText(fullText),
      
      // 尝试修复常见JSON错误并解析
      () => {
        // 尝试移除可能干扰的前缀文本
        const jsonStart = fullText.indexOf('{');
        if (jsonStart > 0) {
          return extractJsonFromText(fullText.substring(jsonStart));
        }
        return null;
      },
      
      // 寻找最后一个完整JSON结构
      () => {
        const matches = fullText.match(/{[\s\S]*}/g);
        if (matches && matches.length > 0) {
          // 尝试最后一个匹配
          const lastMatch = matches[matches.length - 1];
          return JSON.parse(lastMatch);
        }
        return null;
      }
    ];
    
    // 依次尝试不同的提取方法
    for (const method of extractionMethods) {
      try {
        const json = method();
        if (json) {
          finalJson = json;
          break;
        }
      } catch (e) {
        // 忽略此方法的错误，尝试下一个
        continue;
      }
    }
    
    if (!finalJson) {
      console.error('JSON解析失败，完整响应内容:', fullText);
      console.error('响应长度:', fullText.length);
      console.error('响应开头:', fullText.substring(0, 500));
      console.error('响应结尾:', fullText.substring(Math.max(0, fullText.length - 500)));

      // 检查响应是否包含常见的错误模式
      if (fullText.includes('error') || fullText.includes('Error')) {
        throw new Error('大模型返回了错误信息，请检查输入内容或稍后重试');
      } else if (fullText.length < 50) {
        throw new Error('大模型返回的内容过短，可能是网络问题或服务异常');
      } else if (!fullText.includes('{') && !fullText.includes('}')) {
        throw new Error('大模型返回的内容不包含JSON格式，请尝试简化描述');
      } else {
        throw new Error('无法从大模型响应中提取有效的JSON结构，请尝试重新生成');
      }
    }

    updateProgress(fullText, 'complete');
    return finalJson;
  } catch (error) {
    console.error('JSON解析最终失败:', error);
    console.error('完整错误信息:', {
      message: error.message,
      stack: error.stack,
      responseLength: fullText ? fullText.length : 0,
      responsePreview: fullText ? fullText.substring(0, 200) + '...' : 'No response'
    });
    updateProgress(fullText || '', 'error');

    // 重新抛出更详细的错误
    if (error.message.includes('大模型')) {
      throw error; // 已经是我们自定义的错误，直接抛出
    } else {
      throw new Error('JSON解析失败: ' + error.message);
    }
  }
};

/**
 * 从LLM响应中提取文本内容
 * @param {Object} response - LLM API响应对象
 * @returns {string} - 提取的文本内容
 */
const extractContentFromResponse = (response) => {
  return response.choices[0]?.message?.content || '';
};

/**
 * 从文本中提取JSON
 * @param {string} text - 包含JSON的文本
 * @returns {Object|null} - 解析后的JSON对象或null
 */
const extractJsonFromText = (text) => {
  try {
    // 尝试多种JSON提取模式
    const jsonMatch = text.match(/```json\n([\s\S]*?)\n```/) || 
                      text.match(/```\n([\s\S]*?)\n```/) || 
                      text.match(/{[\s\S]*}/);
    
    if (jsonMatch) {
      const jsonString = jsonMatch[1] || jsonMatch[0];
      return JSON.parse(jsonString);
    }
    
    // 如果上述模式都未匹配，尝试直接解析整个文本
    return JSON.parse(text);
  } catch (error) {
    // 解析失败时不抛出错误，返回null
    return null;
  }
};

/**
 * 调用LLM并获取文本响应
 * @param {string} prompt - 提示文本
 * @param {Object} options - 配置选项
 * @returns {Promise<string>} - 返回提取的文本内容
 */
const getLLMResponse = async (prompt, options = {}) => {
  const response = await callLLM(prompt, options);
  return extractContentFromResponse(response);
};

/**
 * 调用LLM并获取JSON响应
 * @param {string} prompt - 提示文本
 * @param {Object} options - 配置选项
 * @returns {Promise<Object>} - 返回解析后的JSON对象
 * @throws {Error} - 如果无法解析JSON，则抛出错误
 */
const getLLMJsonResponse = async (prompt, options = {}) => {
  const content = await getLLMResponse(prompt, options);
  const json = extractJsonFromText(content);
  
  if (!json) {
    throw new Error('无法从LLM响应中解析JSON');
  }
  
  return json;
};

export {
  callLLM,
  callLLMStream,
  streamJsonResponse,
  getLLMResponse,
  getLLMJsonResponse,
  extractContentFromResponse,
  extractJsonFromText
}; 