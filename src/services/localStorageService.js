/**
 * 本地存储服务 - 临时替代ElasticSearch的轻量级存储方案
 * 使用浏览器的localStorage和IndexedDB
 */

/**
 * 本地存储服务类
 */
class LocalStorageService {
  constructor() {
    this.storageKey = 'promptEvaluationData';
    this.dbName = 'PromptEvaluationDB';
    this.dbVersion = 2; // 增加版本号以确保新的对象存储被创建
    this.db = null;
    this.dbReady = false;

    // 初始化IndexedDB
    this.dbInitPromise = this.initIndexedDB();
  }

  /**
   * 初始化IndexedDB
   */
  async initIndexedDB() {
    return new Promise((resolve, reject) => {
      try {
        if (typeof window !== 'undefined' && window.indexedDB) {
          const request = indexedDB.open(this.dbName, this.dbVersion);

          request.onerror = () => {
            console.warn('IndexedDB初始化失败，将使用localStorage');
            this.dbReady = false;
            resolve(false);
          };

          request.onsuccess = (event) => {
            this.db = event.target.result;
            this.dbReady = true;
            console.log('IndexedDB初始化成功');
            resolve(true);
          };

          request.onupgradeneeded = (event) => {
            const db = event.target.result;
            console.log('IndexedDB升级中，当前版本:', event.oldVersion, '目标版本:', event.newVersion);

            // 创建prompts对象存储
            if (!db.objectStoreNames.contains('prompts')) {
              const promptStore = db.createObjectStore('prompts', { keyPath: 'id' });
              promptStore.createIndex('type', 'type', { unique: false });
              promptStore.createIndex('score', 'score', { unique: false });
              promptStore.createIndex('timestamp', 'timestamp', { unique: false });
              console.log('创建prompts对象存储');
            }

            // 创建evaluations对象存储
            if (!db.objectStoreNames.contains('evaluations')) {
              const evalStore = db.createObjectStore('evaluations', { keyPath: 'id' });
              evalStore.createIndex('workflowId', 'workflowId', { unique: false });
              evalStore.createIndex('timestamp', 'timestamp', { unique: false });
              evalStore.createIndex('evaluationType', 'evaluationType', { unique: false });
              evalStore.createIndex('promptType', 'promptType', { unique: false });
              console.log('创建evaluations对象存储');
            }

            // 创建llmEvaluations对象存储（LLM-as-a-judge评估）
            if (!db.objectStoreNames.contains('llmEvaluations')) {
              const llmEvalStore = db.createObjectStore('llmEvaluations', { keyPath: 'id' });
              llmEvalStore.createIndex('workflowId', 'workflowId', { unique: false });
              llmEvalStore.createIndex('timestamp', 'timestamp', { unique: false });
              llmEvalStore.createIndex('promptType', 'promptType', { unique: false });
              console.log('创建llmEvaluations对象存储');
            }

            // 创建manualEvaluations对象存储（人工评估）
            if (!db.objectStoreNames.contains('manualEvaluations')) {
              const manualEvalStore = db.createObjectStore('manualEvaluations', { keyPath: 'id' });
              manualEvalStore.createIndex('workflowId', 'workflowId', { unique: false });
              manualEvalStore.createIndex('timestamp', 'timestamp', { unique: false });
              manualEvalStore.createIndex('promptType', 'promptType', { unique: false });
              console.log('创建manualEvaluations对象存储');
            }
          };
        } else {
          console.warn('IndexedDB不可用，将使用localStorage');
          this.dbReady = false;
          resolve(false);
        }
      } catch (error) {
        console.warn('IndexedDB不可用，将使用localStorage:', error);
        this.dbReady = false;
        resolve(false);
      }
    });
  }

  /**
   * 确保数据库已准备就绪
   */
  async ensureDBReady() {
    if (this.dbInitPromise) {
      await this.dbInitPromise;
    }
    return this.dbReady;
  }

  /**
   * 保存提示词
   */
  async savePrompt(promptData) {
    try {
      const document = {
        ...promptData,
        timestamp: new Date().toISOString(),
        metadata: {
          length: promptData.content.length,
          wordCount: promptData.content.split(/\s+/).length,
          language: 'zh-CN',
          ...promptData.metadata
        }
      };

      // 尝试使用IndexedDB
      if (this.db) {
        const transaction = this.db.transaction(['prompts'], 'readwrite');
        const store = transaction.objectStore('prompts');
        await store.put(document);
        console.log(`提示词保存到IndexedDB成功，ID: ${promptData.id}`);
      } else {
        // 回退到localStorage
        const existingData = this.getLocalStorageData();
        existingData.prompts.push(document);
        this.saveLocalStorageData(existingData);
        console.log(`提示词保存到localStorage成功，ID: ${promptData.id}`);
      }

      return { success: true, id: promptData.id };
    } catch (error) {
      console.error('保存提示词失败:', error);
      throw error;
    }
  }

  /**
   * 保存评估结果（兼容旧版本）
   */
  async saveEvaluation(evaluationData) {
    try {
      const document = {
        ...evaluationData,
        timestamp: new Date().toISOString()
      };

      // 尝试使用IndexedDB
      if (this.db) {
        const transaction = this.db.transaction(['evaluations'], 'readwrite');
        const store = transaction.objectStore('evaluations');
        await store.put(document);
        console.log(`评估结果保存到IndexedDB成功，ID: ${evaluationData.id}`);
      } else {
        // 回退到localStorage
        const existingData = this.getLocalStorageData();
        existingData.evaluations.push(document);
        this.saveLocalStorageData(existingData);
        console.log(`评估结果保存到localStorage成功，ID: ${evaluationData.id}`);
      }

      return { success: true, id: evaluationData.id };
    } catch (error) {
      console.error('保存评估结果失败:', error);
      throw error;
    }
  }

  /**
   * 保存LLM评估结果
   */
  async saveLLMEvaluation(evaluationData) {
    try {
      const document = {
        ...evaluationData,
        evaluationType: 'llm',
        timestamp: new Date().toISOString()
      };

      // 尝试使用IndexedDB
      if (this.db) {
        const transaction = this.db.transaction(['llmEvaluations'], 'readwrite');
        const store = transaction.objectStore('llmEvaluations');
        await store.put(document);
        console.log(`LLM评估结果保存到IndexedDB成功，ID: ${evaluationData.id}`);
      } else {
        // 回退到localStorage
        const existingData = this.getLocalStorageData();
        if (!existingData.llmEvaluations) {
          existingData.llmEvaluations = [];
        }
        existingData.llmEvaluations.push(document);
        this.saveLocalStorageData(existingData);
        console.log(`LLM评估结果保存到localStorage成功，ID: ${evaluationData.id}`);
      }

      return { success: true, id: evaluationData.id };
    } catch (error) {
      console.error('保存LLM评估结果失败:', error);
      throw error;
    }
  }

  /**
   * 保存人工评估结果
   */
  async saveManualEvaluation(evaluationData) {
    try {
      const document = {
        ...evaluationData,
        evaluationType: 'manual',
        timestamp: new Date().toISOString()
      };

      // 尝试使用IndexedDB
      if (this.db) {
        const transaction = this.db.transaction(['manualEvaluations'], 'readwrite');
        const store = transaction.objectStore('manualEvaluations');
        await store.put(document);
        console.log(`人工评估结果保存到IndexedDB成功，ID: ${evaluationData.id}`);
      } else {
        // 回退到localStorage
        const existingData = this.getLocalStorageData();
        if (!existingData.manualEvaluations) {
          existingData.manualEvaluations = [];
        }
        existingData.manualEvaluations.push(document);
        this.saveLocalStorageData(existingData);
        console.log(`人工评估结果保存到localStorage成功，ID: ${evaluationData.id}`);
      }

      return { success: true, id: evaluationData.id };
    } catch (error) {
      console.error('保存人工评估结果失败:', error);
      throw error;
    }
  }

  /**
   * 搜索提示词
   */
  async searchPrompts(query, options = {}) {
    try {
      let prompts = [];

      // 尝试从IndexedDB获取
      if (this.db) {
        const transaction = this.db.transaction(['prompts'], 'readonly');
        const store = transaction.objectStore('prompts');
        const request = store.getAll();
        
        prompts = await new Promise((resolve, reject) => {
          request.onsuccess = () => resolve(request.result);
          request.onerror = () => reject(request.error);
        });
      } else {
        // 从localStorage获取
        const data = this.getLocalStorageData();
        prompts = data.prompts;
      }

      // 过滤和搜索
      let filteredPrompts = prompts;

      // 文本搜索
      if (query) {
        filteredPrompts = filteredPrompts.filter(prompt =>
          prompt.content.toLowerCase().includes(query.toLowerCase())
        );
      }

      // 类型过滤
      if (options.type) {
        filteredPrompts = filteredPrompts.filter(prompt => prompt.type === options.type);
      }

      // 分数过滤
      if (options.minScore) {
        filteredPrompts = filteredPrompts.filter(prompt => prompt.score >= options.minScore);
      }

      // 排序
      filteredPrompts.sort((a, b) => {
        if (b.score !== a.score) return b.score - a.score;
        return new Date(b.timestamp) - new Date(a.timestamp);
      });

      // 分页
      const from = options.from || 0;
      const size = options.size || 10;
      const paginatedPrompts = filteredPrompts.slice(from, from + size);

      return {
        total: filteredPrompts.length,
        prompts: paginatedPrompts.map(prompt => ({
          id: prompt.id,
          score: 1.0, // 模拟搜索分数
          ...prompt
        }))
      };
    } catch (error) {
      console.error('搜索提示词失败:', error);
      throw error;
    }
  }

  /**
   * 获取评估统计信息
   */
  async getEvaluationStatistics() {
    try {
      let evaluations = [];

      // 尝试从IndexedDB获取
      if (this.db) {
        const transaction = this.db.transaction(['evaluations'], 'readonly');
        const store = transaction.objectStore('evaluations');
        const request = store.getAll();
        
        evaluations = await new Promise((resolve, reject) => {
          request.onsuccess = () => resolve(request.result);
          request.onerror = () => reject(request.error);
        });
      } else {
        // 从localStorage获取
        const data = this.getLocalStorageData();
        evaluations = data.evaluations;
      }

      if (evaluations.length === 0) {
        return {
          totalEvaluations: 0,
          averageOriginalScore: 0,
          averageEnhancedScore: 0,
          averageImprovement: 0,
          improvementLevels: [],
          scoreDistribution: []
        };
      }

      // 计算统计信息
      const originalScores = evaluations.map(e => e.originalPrompt.score);
      const enhancedScores = evaluations.map(e => e.enhancedPrompt.score);
      const improvements = evaluations.map(e => e.improvement.absoluteImprovement);

      const avgOriginal = originalScores.reduce((a, b) => a + b, 0) / originalScores.length;
      const avgEnhanced = enhancedScores.reduce((a, b) => a + b, 0) / enhancedScores.length;
      const avgImprovement = improvements.reduce((a, b) => a + b, 0) / improvements.length;

      // 改进等级统计
      const improvementLevels = {};
      evaluations.forEach(e => {
        const level = e.improvement.improvementLevel;
        improvementLevels[level] = (improvementLevels[level] || 0) + 1;
      });

      // 分数分布
      const scoreDistribution = {};
      enhancedScores.forEach(score => {
        const bucket = Math.floor(score / 10) * 10;
        scoreDistribution[bucket] = (scoreDistribution[bucket] || 0) + 1;
      });

      return {
        totalEvaluations: evaluations.length,
        averageOriginalScore: Math.round(avgOriginal * 100) / 100,
        averageEnhancedScore: Math.round(avgEnhanced * 100) / 100,
        averageImprovement: Math.round(avgImprovement * 100) / 100,
        improvementLevels: Object.entries(improvementLevels).map(([key, doc_count]) => ({ key, doc_count })),
        scoreDistribution: Object.entries(scoreDistribution).map(([key, doc_count]) => ({ key: parseInt(key), doc_count }))
      };
    } catch (error) {
      console.error('获取评估统计信息失败:', error);
      throw error;
    }
  }

  /**
   * 检查连接状态
   */
  async checkConnection() {
    try {
      // 检查localStorage是否可用
      if (typeof window !== 'undefined' && window.localStorage) {
        // 测试写入
        localStorage.setItem('test', 'test');
        localStorage.removeItem('test');
        console.log('本地存储连接正常');
        return true;
      }
      return false;
    } catch (error) {
      console.error('本地存储连接失败:', error);
      return false;
    }
  }

  /**
   * 从localStorage获取数据
   */
  getLocalStorageData() {
    try {
      const data = localStorage.getItem(this.storageKey);
      return data ? JSON.parse(data) : {
        prompts: [],
        evaluations: [],
        llmEvaluations: [],
        manualEvaluations: []
      };
    } catch (error) {
      console.warn('读取localStorage失败:', error);
      return {
        prompts: [],
        evaluations: [],
        llmEvaluations: [],
        manualEvaluations: []
      };
    }
  }

  /**
   * 保存数据到localStorage
   */
  saveLocalStorageData(data) {
    try {
      localStorage.setItem(this.storageKey, JSON.stringify(data));
    } catch (error) {
      console.error('保存到localStorage失败:', error);
      throw error;
    }
  }

  /**
   * 清空所有数据
   */
  async clearAllData() {
    try {
      // 清空IndexedDB
      if (this.db) {
        const storeNames = ['prompts', 'evaluations'];

        // 检查并添加新的对象存储
        if (this.db.objectStoreNames.contains('llmEvaluations')) {
          storeNames.push('llmEvaluations');
        }
        if (this.db.objectStoreNames.contains('manualEvaluations')) {
          storeNames.push('manualEvaluations');
        }

        const transaction = this.db.transaction(storeNames, 'readwrite');

        for (const storeName of storeNames) {
          await transaction.objectStore(storeName).clear();
        }
      }

      // 清空localStorage
      localStorage.removeItem(this.storageKey);

      console.log('所有数据已清空');
    } catch (error) {
      console.error('清空数据失败:', error);
      throw error;
    }
  }

  /**
   * 导出数据
   */
  async exportData() {
    try {
      let prompts = [];
      let evaluations = [];

      if (this.db) {
        const transaction = this.db.transaction(['prompts', 'evaluations'], 'readonly');
        
        prompts = await new Promise((resolve, reject) => {
          const request = transaction.objectStore('prompts').getAll();
          request.onsuccess = () => resolve(request.result);
          request.onerror = () => reject(request.error);
        });
        
        evaluations = await new Promise((resolve, reject) => {
          const request = transaction.objectStore('evaluations').getAll();
          request.onsuccess = () => resolve(request.result);
          request.onerror = () => reject(request.error);
        });
      } else {
        const data = this.getLocalStorageData();
        prompts = data.prompts;
        evaluations = data.evaluations;
      }

      return {
        prompts,
        evaluations,
        exportTime: new Date().toISOString(),
        version: '1.0'
      };
    } catch (error) {
      console.error('导出数据失败:', error);
      throw error;
    }
  }
  /**
   * 获取LLM评估结果
   */
  async getLLMEvaluations(promptId = null) {
    try {
      let evaluations = [];

      // 确保数据库已准备就绪
      await this.ensureDBReady();

      if (this.db && this.dbReady) {
        try {
          // 检查对象存储是否存在
          if (!this.db.objectStoreNames.contains('llmEvaluations')) {
            console.warn('llmEvaluations对象存储不存在，使用localStorage');
            const data = this.getLocalStorageData();
            evaluations = data.llmEvaluations || [];
          } else {
            const transaction = this.db.transaction(['llmEvaluations'], 'readonly');
            const store = transaction.objectStore('llmEvaluations');

            if (promptId) {
              const index = store.index('workflowId');
              const request = index.getAll(promptId);
              evaluations = await new Promise((resolve, reject) => {
                request.onsuccess = () => resolve(request.result);
                request.onerror = () => reject(request.error);
              });
            } else {
              const request = store.getAll();
              evaluations = await new Promise((resolve, reject) => {
                request.onsuccess = () => resolve(request.result);
                request.onerror = () => reject(request.error);
              });
            }
          }
        } catch (dbError) {
          console.warn('IndexedDB访问失败，回退到localStorage:', dbError);
          const data = this.getLocalStorageData();
          evaluations = data.llmEvaluations || [];
        }
      } else {
        const data = this.getLocalStorageData();
        evaluations = data.llmEvaluations || [];
      }

      if (promptId && evaluations.length > 0) {
        evaluations = evaluations.filter(evaluation => evaluation.workflowId === promptId);
      }

      return evaluations;
    } catch (error) {
      console.error('获取LLM评估结果失败:', error);
      // 返回空数组而不是抛出错误，避免页面崩溃
      return [];
    }
  }

  /**
   * 获取人工评估结果
   */
  async getManualEvaluations(promptId = null) {
    try {
      let evaluations = [];

      // 确保数据库已准备就绪
      await this.ensureDBReady();

      if (this.db && this.dbReady) {
        try {
          // 检查对象存储是否存在
          if (!this.db.objectStoreNames.contains('manualEvaluations')) {
            console.warn('manualEvaluations对象存储不存在，使用localStorage');
            const data = this.getLocalStorageData();
            evaluations = data.manualEvaluations || [];
          } else {
            const transaction = this.db.transaction(['manualEvaluations'], 'readonly');
            const store = transaction.objectStore('manualEvaluations');

            if (promptId) {
              const index = store.index('workflowId');
              const request = index.getAll(promptId);
              evaluations = await new Promise((resolve, reject) => {
                request.onsuccess = () => resolve(request.result);
                request.onerror = () => reject(request.error);
              });
            } else {
              const request = store.getAll();
              evaluations = await new Promise((resolve, reject) => {
                request.onsuccess = () => resolve(request.result);
                request.onerror = () => reject(request.error);
              });
            }
          }
        } catch (dbError) {
          console.warn('IndexedDB访问失败，回退到localStorage:', dbError);
          const data = this.getLocalStorageData();
          evaluations = data.manualEvaluations || [];
        }
      } else {
        const data = this.getLocalStorageData();
        evaluations = data.manualEvaluations || [];
      }

      if (promptId && evaluations.length > 0) {
        evaluations = evaluations.filter(evaluation => evaluation.workflowId === promptId);
      }

      return evaluations;
    } catch (error) {
      console.error('获取人工评估结果失败:', error);
      // 返回空数组而不是抛出错误，避免页面崩溃
      return [];
    }
  }

  /**
   * 获取指定提示词的所有评估结果
   */
  async getAllEvaluationsForPrompt(promptId) {
    try {
      const [llmEvaluations, manualEvaluations] = await Promise.all([
        this.getLLMEvaluations(promptId),
        this.getManualEvaluations(promptId)
      ]);

      return {
        llmEvaluations,
        manualEvaluations,
        totalLLMEvaluations: llmEvaluations.length,
        totalManualEvaluations: manualEvaluations.length
      };
    } catch (error) {
      console.error('获取提示词评估结果失败:', error);
      throw error;
    }
  }
}

// 导出单例实例
export const localStorageService = new LocalStorageService();

export default LocalStorageService;
