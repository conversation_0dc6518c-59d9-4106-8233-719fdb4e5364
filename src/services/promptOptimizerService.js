import { getLLMResponse, streamJsonResponse } from './llmService';
import {
  createAutoOptimizePrompt,
  createAutoOptimizePromptVersionB,
  createGuidedOptimizePrompt
} from '../prompts/promptOptimization';
import { createPromptEvaluationPrompt } from '../prompts/promptEvaluation';

/**
 * Prompt优化服务 - 处理自动优化和引导式优化
 */

/**
 * 自动优化Prompt（流式响应版本 - Version A）
 * @param {string} originalPrompt - 原始提示词
 * @param {Function} onStreamChunk - 处理流式数据块的回调
 * @returns {Promise<Object>} - 优化结果
 */
const autoOptimizePromptStream = async (originalPrompt, onStreamChunk = () => {}) => {
  try {
    // 使用流式处理获取响应并实时更新
    let result = null;
    
    // 使用新的prompt模板
    const fullPrompt = createAutoOptimizePrompt(originalPrompt);

    try {
      // 尝试使用流式JSON处理
      await streamJsonResponse(
        fullPrompt, 
        { temperature: 0.3 }, 
        (text, progress) => {
          // 每次收到新的文本片段，就回调给界面
          onStreamChunk(text, progress);
          
          // 如果已经解析出了JSON，保存结果
          if (progress.json) {
            result = progress.json;
          }
        }
      );
      
      // 如果streamJsonResponse成功解析出了JSON，直接返回
      if (result) {
        return result;
      }
    } catch (streamError) {
      console.error('流式JSON处理失败，尝试使用备用解析方法:', streamError);
      // 流式处理失败，继续使用备用方法
    }
    
    // 兜底：如果流式解析失败，尝试使用常规响应
    console.log('流式JSON处理未成功，切换到常规API调用');
    const responseText = await getLLMResponse(fullPrompt, { 
      temperature: 0.3,
      max_tokens: 4000
    });
    
    console.log('常规API调用完成，尝试解析JSON');
    // 尝试多种方式解析JSON
    let jsonData = null;
    
    // 方法1: 使用常规JSON提取
    try {
      const jsonMatch = responseText.match(/{[\s\S]*}/);
      if (jsonMatch) {
        jsonData = JSON.parse(jsonMatch[0]);
        console.log('成功通过正则匹配解析JSON');
      }
    } catch (parseError) {
      console.error('常规JSON提取失败:', parseError);
    }
    
    // 方法2: 尝试提取代码块中的JSON
    if (!jsonData) {
      try {
        const codeBlockMatch = responseText.match(/```(?:json)?\n([\s\S]*?)\n```/);
        if (codeBlockMatch && codeBlockMatch[1]) {
          jsonData = JSON.parse(codeBlockMatch[1]);
          console.log('成功从代码块中提取JSON');
        }
      } catch (codeBlockError) {
        console.error('从代码块提取JSON失败:', codeBlockError);
      }
    }
    
    // 方法3: 尝试解析整个文本
    if (!jsonData) {
      try {
        // 最后尝试整个响应作为JSON
        jsonData = JSON.parse(responseText);
        console.log('成功解析整个响应为JSON');
      } catch (fullTextError) {
        console.error('整个响应解析为JSON失败:', fullTextError);
      }
    }
    
    // 方法4: 如果还是没有成功，构造兜底JSON
    if (!jsonData) {
      console.warn('所有JSON解析方法都失败，构造兜底JSON');
      // 构造紧急兜底JSON
      jsonData = {
        optimizedPrompt: responseText,
        improvementPoints: [
          {
            type: "格式化输出",
            description: "将AI输出格式化为更易读的形式"
          }
        ]
      };
    }
    
    return jsonData;
  } catch (error) {
    console.error('Prompt自动优化流式处理失败:', error);
    throw new Error(`Prompt优化失败: ${error.message}`);
  }
};

/**
 * 自动优化Prompt（流式响应版本 - Version B）
 * @param {string} originalPrompt - 原始提示词
 * @param {Function} onStreamChunk - 处理流式数据块的回调
 * @returns {Promise<Object>} - 优化结果
 */
const autoOptimizePromptStreamVersionB = async (originalPrompt, onStreamChunk = () => {}) => {
  try {
    // 使用流式处理获取响应并实时更新
    let result = null;

    // 使用新的prompt模板
    const fullPrompt = createAutoOptimizePromptVersionB(originalPrompt);

    try {
      // 尝试使用流式JSON处理
      await streamJsonResponse(
        fullPrompt,
        { temperature: 0.3 },
        (text, progress) => {
          // 处理Version B的特殊格式
          let extractedPrompt = '';

          // 尝试提取<augment-enhanced-prompt>标签中的内容
          const promptMatch = text.match(/<augment-enhanced-prompt>([\s\S]*?)<\/augment-enhanced-prompt>/);
          if (promptMatch && promptMatch[1]) {
            extractedPrompt = promptMatch[1].trim();
          } else {
            // 如果没有找到标签，尝试提取### BEGIN RESPONSE ###之后的内容
            const responseMatch = text.match(/### BEGIN RESPONSE ###([\s\S]*?)(?:### END RESPONSE ###|$)/);
            if (responseMatch && responseMatch[1]) {
              const responseContent = responseMatch[1].trim();
              // 再次尝试提取标签内容
              const innerPromptMatch = responseContent.match(/<augment-enhanced-prompt>([\s\S]*?)<\/augment-enhanced-prompt>/);
              if (innerPromptMatch && innerPromptMatch[1]) {
                extractedPrompt = innerPromptMatch[1].trim();
              } else {
                // 如果还是没有，就使用整个响应内容（去掉格式标记）
                extractedPrompt = responseContent
                  .replace(/Here is an enhanced version.*?:/i, '')
                  .replace(/<augment-enhanced-prompt>/g, '')
                  .replace(/<\/augment-enhanced-prompt>/g, '')
                  .trim();
              }
            }
          }

          // 如果成功提取到内容，构造JSON格式
          if (extractedPrompt) {
            const mockProgress = {
              ...progress,
              json: {
                optimizedPrompt: extractedPrompt,
                improvementPoints: [
                  {
                    type: "Version B优化",
                    description: "使用简化的指令重写方法进行优化"
                  },
                  {
                    type: "格式处理",
                    description: "从特定格式标签中提取优化后的提示词"
                  }
                ]
              }
            };
            onStreamChunk(text, mockProgress);
            result = mockProgress.json;
          } else {
            // 如果没有提取到，就传递原始文本
            onStreamChunk(text, progress);
            if (progress.json) {
              result = progress.json;
            }
          }
        }
      );

      // 如果streamJsonResponse成功解析出了JSON，直接返回
      if (result) {
        return result;
      }
    } catch (streamError) {
      console.error('Version B流式JSON处理失败，尝试使用备用解析方法:', streamError);
    }

    // 兜底：如果流式解析失败，尝试使用常规响应
    console.log('Version B流式JSON处理未成功，切换到常规API调用');
    const responseText = await getLLMResponse(fullPrompt, {
      temperature: 0.3,
      max_tokens: 4000
    });

    // 尝试从响应中提取优化后的提示词
    let extractedPrompt = '';

    // 方法1: 提取<augment-enhanced-prompt>标签中的内容
    const promptMatch = responseText.match(/<augment-enhanced-prompt>([\s\S]*?)<\/augment-enhanced-prompt>/);
    if (promptMatch && promptMatch[1]) {
      extractedPrompt = promptMatch[1].trim();
    } else {
      // 方法2: 提取### BEGIN RESPONSE ###之后的内容
      const responseMatch = responseText.match(/### BEGIN RESPONSE ###([\s\S]*?)(?:### END RESPONSE ###|$)/);
      if (responseMatch && responseMatch[1]) {
        const responseContent = responseMatch[1].trim();
        // 清理格式标记
        extractedPrompt = responseContent
          .replace(/Here is an enhanced version.*?:/i, '')
          .replace(/<augment-enhanced-prompt>/g, '')
          .replace(/<\/augment-enhanced-prompt>/g, '')
          .trim();
      } else {
        // 方法3: 使用整个响应作为兜底
        extractedPrompt = responseText;
      }
    }

    // 构造返回结果
    const jsonData = {
      optimizedPrompt: extractedPrompt,
      improvementPoints: [
        {
          type: "Version B优化",
          description: "使用简化的指令重写方法进行优化"
        },
        {
          type: "直接改写",
          description: "专注于提高指令的清晰度和具体性"
        }
      ]
    };

    return jsonData;
  } catch (error) {
    console.error('Prompt自动优化Version B流式处理失败:', error);
    throw new Error(`Prompt优化Version B失败: ${error.message}`);
  }
};

/**
 * 引导式优化Prompt（流式响应版本）
 * @param {string} originalPrompt - 原始提示词
 * @param {string} guidanceText - 引导词
 * @param {Function} onStreamChunk - 处理流式数据块的回调
 * @returns {Promise<Object>} - 优化结果
 */
const guidedOptimizePromptStream = async (originalPrompt, guidanceText, onStreamChunk = () => {}) => {
  try {
    // 使用流式处理获取响应并实时更新
    let result = null;
    
    // 使用新的prompt模板
    const fullPrompt = createGuidedOptimizePrompt(originalPrompt, guidanceText);
    
    try {
      // 尝试使用流式JSON处理
      // 使用streamJsonResponse在UI上实现平滑的流式生成效果
      await streamJsonResponse(
        fullPrompt, 
        { temperature: 0.3 }, 
        (text, progress) => {
          // 每次收到新的文本片段，就回调给界面
          onStreamChunk(text, progress);
          
          // 如果已经解析出了JSON，保存结果
          if (progress.json) {
            result = progress.json;
          }
        }
      );
      
      // 如果streamJsonResponse成功解析出了JSON，直接返回
      if (result) {
        return result;
      }
    } catch (streamError) {
      console.error('引导式优化流式JSON处理失败，尝试使用备用解析方法:', streamError);
      // 流式处理失败，继续使用备用方法
    }
    
    // 兜底：如果流式解析失败，尝试使用常规响应
    console.log('引导式优化流式JSON处理未成功，切换到常规API调用');
    const responseText = await getLLMResponse(fullPrompt, { 
      temperature: 0.3,
      max_tokens: 4000
    });
    
    console.log('引导式优化常规API调用完成，尝试解析JSON');
    // 尝试多种方式解析JSON
    let jsonData = null;
    
    // 方法1: 使用常规JSON提取
    try {
      const jsonMatch = responseText.match(/{[\s\S]*}/);
      if (jsonMatch) {
        jsonData = JSON.parse(jsonMatch[0]);
        console.log('成功通过正则匹配解析JSON');
      }
    } catch (parseError) {
      console.error('常规JSON提取失败:', parseError);
    }
    
    // 方法2: 尝试提取代码块中的JSON
    if (!jsonData) {
      try {
        const codeBlockMatch = responseText.match(/```(?:json)?\n([\s\S]*?)\n```/);
        if (codeBlockMatch && codeBlockMatch[1]) {
          jsonData = JSON.parse(codeBlockMatch[1]);
          console.log('成功从代码块中提取JSON');
        }
      } catch (codeBlockError) {
        console.error('从代码块提取JSON失败:', codeBlockError);
      }
    }
    
    // 方法3: 尝试解析整个文本
    if (!jsonData) {
      try {
        // 最后尝试整个响应作为JSON
        jsonData = JSON.parse(responseText);
        console.log('成功解析整个响应为JSON');
      } catch (fullTextError) {
        console.error('整个响应解析为JSON失败:', fullTextError);
      }
    }
    
    // 方法4: 如果还是没有成功，构造兜底JSON
    if (!jsonData) {
      console.warn('所有JSON解析方法都失败，构造兜底JSON');
      // 构造紧急兜底JSON
      jsonData = {
        optimizedPrompt: responseText,
        guidedImprovements: [
          {
            type: "引导式优化",
            description: "基于您的引导说明对提示词进行了优化"
          }
        ]
      };
    }
    
    return jsonData;
  } catch (error) {
    console.error('Prompt引导式优化流式处理失败:', error);
    throw new Error(`Prompt优化失败: ${error.message}`);
  }
};

/**
 * 自动优化Prompt - 向后兼容的包装函数
 */
const autoOptimizePrompt = async (originalPrompt) => {
  return autoOptimizePromptStream(originalPrompt, () => {});
};

/**
 * 引导式优化Prompt - 向后兼容的包装函数
 */
const guidedOptimizePrompt = async (originalPrompt, guidanceText) => {
  return guidedOptimizePromptStream(originalPrompt, guidanceText, () => {});
};

/**
 * 评估Prompt质量
 * @param {string} originalPrompt - 原始提示词
 * @param {string} optimizedPrompt - 优化后的提示词
 * @returns {Promise<Object>} - 评估结果
 */
const evaluatePromptQuality = async (originalPrompt, optimizedPrompt) => {
  // 使用新的prompt模板
  const fullPrompt = createPromptEvaluationPrompt(originalPrompt, optimizedPrompt);

  try {
    const responseText = await getLLMResponse(fullPrompt, {
      temperature: 0.2,
      max_tokens: 3000
    });

    // 尝试解析JSON响应
    let evaluationData = null;

    // 方法1: 使用常规JSON提取
    try {
      const jsonMatch = responseText.match(/{[\s\S]*}/);
      if (jsonMatch) {
        evaluationData = JSON.parse(jsonMatch[0]);
      }
    } catch (parseError) {
      console.error('JSON解析失败:', parseError);
    }

    // 方法2: 尝试提取代码块中的JSON
    if (!evaluationData) {
      try {
        const codeBlockMatch = responseText.match(/```(?:json)?\n([\s\S]*?)\n```/);
        if (codeBlockMatch && codeBlockMatch[1]) {
          evaluationData = JSON.parse(codeBlockMatch[1]);
        }
      } catch (codeBlockError) {
        console.error('从代码块提取JSON失败:', codeBlockError);
      }
    }

    // 如果解析失败，返回默认结构
    if (!evaluationData) {
      console.warn('评估结果解析失败，返回默认结构');
      evaluationData = {
        originalEvaluation: {
          scores: { clarity: 5, structure: 5, completeness: 5, specificity: 5, roleDefinition: 5, outputFormat: 5, constraints: 5, actionability: 5 },
          totalScore: 40,
          averageScore: 5.0,
          strengths: ["需要进一步分析"],
          weaknesses: ["评估数据解析失败"]
        },
        optimizedEvaluation: {
          scores: { clarity: 7, structure: 7, completeness: 7, specificity: 7, roleDefinition: 7, outputFormat: 7, constraints: 7, actionability: 7 },
          totalScore: 56,
          averageScore: 7.0,
          strengths: ["经过优化处理"],
          weaknesses: ["评估数据解析失败"]
        },
        comparison: {
          winner: "optimized",
          improvementAreas: ["整体结构", "指令清晰度"],
          recommendation: "建议使用优化后的版本",
          keyDifferences: ["评估功能需要进一步完善"]
        }
      };
    }

    return evaluationData;
  } catch (error) {
    console.error('Prompt质量评估失败:', error);
    throw new Error(`Prompt评估失败: ${error.message}`);
  }
};

export {
  autoOptimizePrompt,
  guidedOptimizePrompt,
  autoOptimizePromptStream,
  autoOptimizePromptStreamVersionB,
  guidedOptimizePromptStream,
  evaluatePromptQuality
};