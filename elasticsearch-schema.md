# Elasticsearch 索引 Schema 文档

本文档描述了提示词评估系统中使用的 Elasticsearch 索引结构。

## 配置信息

- **Elasticsearch URL**: `http://localhost:9200` (通过代理 `http://localhost:9201` 访问)
- **索引前缀**: 无
- **认证**: 开发环境无需认证

## 索引列表

### 1. prompts 索引

用于存储提示词数据，包括原始提示词和优化后的提示词。

```json
{
  "mappings": {
    "properties": {
      "id": { 
        "type": "keyword" 
      },
      "content": { 
        "type": "text",
        "analyzer": "standard",
        "fields": {
          "keyword": { "type": "keyword" }
        }
      },
      "type": { 
        "type": "keyword",
        "description": "提示词类型: 'original' 或 'enhanced'"
      },
      "score": { 
        "type": "float",
        "description": "提示词评分 (0-100)"
      },
      "evaluationId": { 
        "type": "keyword",
        "description": "关联的评估记录ID"
      },
      "timestamp": { 
        "type": "date",
        "description": "创建时间"
      },
      "metadata": {
        "properties": {
          "length": { 
            "type": "integer",
            "description": "提示词字符长度"
          },
          "wordCount": { 
            "type": "integer",
            "description": "提示词词数"
          },
          "language": { 
            "type": "keyword",
            "description": "语言标识，如 'zh-CN'"
          },
          "category": { 
            "type": "keyword",
            "description": "提示词分类"
          },
          "tags": { 
            "type": "keyword",
            "description": "标签数组"
          }
        }
      }
    }
  }
}
```

### 2. evaluations 索引

用于存储 LLM-as-a-Judge 评估结果，包括原始提示词和优化提示词的对比评估。

```json
{
  "mappings": {
    "properties": {
      "id": { 
        "type": "keyword",
        "description": "评估记录唯一标识"
      },
      "workflowId": { 
        "type": "keyword",
        "description": "工作流ID，用于关联相关评估"
      },
      "timestamp": { 
        "type": "date",
        "description": "评估时间"
      },
      "originalPrompt": {
        "properties": {
          "content": { 
            "type": "text",
            "description": "原始提示词内容"
          },
          "score": { 
            "type": "float",
            "description": "原始提示词总分 (0-100)"
          },
          "evaluation": {
            "properties": {
              "finalScore": { 
                "type": "float",
                "description": "最终评分"
              },
              "llmEvaluation": {
                "properties": {
                  "specificity": { 
                    "type": "float",
                    "description": "具体性评分 (0-100)"
                  },
                  "clarity": { 
                    "type": "float",
                    "description": "清晰度评分 (0-100)"
                  },
                  "structure": { 
                    "type": "float",
                    "description": "结构性评分 (0-100)"
                  },
                  "completeness": { 
                    "type": "float",
                    "description": "完整性评分 (0-100)"
                  },
                  "roleDefinition": { 
                    "type": "float",
                    "description": "角色定义评分 (0-100)"
                  },
                  "outputFormat": { 
                    "type": "float",
                    "description": "输出格式评分 (0-100)"
                  },
                  "constraints": { 
                    "type": "float",
                    "description": "约束条件评分 (0-100)"
                  },
                  "actionability": { 
                    "type": "float",
                    "description": "可操作性评分 (0-100)"
                  }
                }
              },
              "humanEvaluation": {
                "properties": {
                  "specificity": { "type": "float" },
                  "clarity": { "type": "float" },
                  "structure": { "type": "float" },
                  "completeness": { "type": "float" },
                  "roleDefinition": { "type": "float" },
                  "outputFormat": { "type": "float" },
                  "constraints": { "type": "float" },
                  "actionability": { "type": "float" }
                }
              }
            }
          }
        }
      },
      "enhancedPrompt": {
        "properties": {
          "content": { 
            "type": "text",
            "description": "优化后提示词内容"
          },
          "score": { 
            "type": "float",
            "description": "优化后提示词总分 (0-100)"
          },
          "evaluation": {
            "properties": {
              "finalScore": { "type": "float" },
              "llmEvaluation": {
                "properties": {
                  "specificity": { "type": "float" },
                  "clarity": { "type": "float" },
                  "structure": { "type": "float" },
                  "completeness": { "type": "float" },
                  "roleDefinition": { "type": "float" },
                  "outputFormat": { "type": "float" },
                  "constraints": { "type": "float" },
                  "actionability": { "type": "float" }
                }
              },
              "humanEvaluation": {
                "properties": {
                  "specificity": { "type": "float" },
                  "clarity": { "type": "float" },
                  "structure": { "type": "float" },
                  "completeness": { "type": "float" },
                  "roleDefinition": { "type": "float" },
                  "outputFormat": { "type": "float" },
                  "constraints": { "type": "float" },
                  "actionability": { "type": "float" }
                }
              }
            }
          }
        }
      },
      "improvement": {
        "properties": {
          "absoluteImprovement": { 
            "type": "float",
            "description": "绝对改进分数"
          },
          "relativeImprovement": { 
            "type": "float",
            "description": "相对改进百分比"
          },
          "improvementLevel": { 
            "type": "keyword",
            "description": "改进等级: '显著改进', '中等改进', '轻微改进', '无改进'"
          },
          "summary": { 
            "type": "text",
            "description": "改进总结文本"
          }
        }
      },
      "metadata": {
        "properties": {
          "evaluationMethod": { 
            "type": "keyword",
            "description": "评估方法: 'LLM-as-a-Judge + Human (optional)'"
          },
          "scoringSystem": { 
            "type": "keyword",
            "description": "评分系统: '0-100 normalized scale'"
          },
          "version": { 
            "type": "keyword",
            "description": "Schema版本"
          }
        }
      }
    }
  }
}
```

## 人工评估数据结构

虽然当前 Elasticsearch schema 中没有专门的人工评估索引，但系统支持人工评估功能。人工评估数据存储在本地 IndexedDB 的 `manualEvaluations` 对象存储中，包含以下结构：

```javascript
{
  id: "manual_timestamp",
  promptType: "original" | "optimized",
  prompt: "提示词内容",
  timestamp: "ISO时间戳",
  evaluationType: "manual",
  scores: {
    efficiency: {
      timeConsumption: 1-10,      // 耗时
      interactionRounds: 1-10,    // 交互轮次
      manualIntervention: 1-10    // 人工介入轮次
    },
    completeness: {
      adherence: 1-10,            // 贴合度
      compliance: 1-10,           // 服从性
      creativity: 1-10            // 创造性
    },
    functionality: {
      basicUsability: 1-10,       // 基本可用性
      functionalCorrectness: 1-10 // 功能正确性
    },
    maintainability: {
      codingStandards: 1-10,      // 编码规范性
      codeComplexity: 1-10,       // 代码复杂度
      codeReadability: 1-10,      // 代码可读性
      codeExtensibility: 1-10,    // 代码可扩展性
      modularity: 1-10            // 模块化程度
    },
    security: {
      securityVulnerabilities: 1-10, // 安全漏洞
      boundaryReliability: 1-10      // 边界可靠性
    },
    compatibility: {
      backwardCompatibility: 1-10,   // 向后兼容性
      forwardCompatibility: 1-10     // 向前兼容性
    },
    stability: {
      performance: 1-10,             // 性能
      runtimeStability: 1-10         // 运行时稳定性
    }
  },
  categoryScores: {
    efficiency: 平均分,
    completeness: 平均分,
    functionality: 平均分,
    maintainability: 平均分,
    security: 平均分,
    compatibility: 平均分,
    stability: 平均分
  },
  totalScore: 总分(1-10),
  normalizedScore: 标准化分数(0-100),
  comments: "评估备注"
}
```

## 索引操作

### 创建索引
索引会在应用启动时自动创建，通过 `elasticsearchService.initializeIndices()` 方法。

### 数据写入
- 提示词数据通过 `elasticsearchService.savePrompt()` 写入
- 评估数据通过 `elasticsearchService.saveEvaluation()` 写入

### 数据查询
- 提示词搜索: `elasticsearchService.searchPrompts()`
- 评估统计: `elasticsearchService.getEvaluationStatistics()`

### 连接检查
通过 `elasticsearchService.checkConnection()` 检查连接状态。

## 注意事项

1. **代理服务器**: 生产环境中通过端口 9201 的代理服务器访问 Elasticsearch，解决 CORS 和 SSL 证书问题
2. **降级策略**: 当 Elasticsearch 不可用时，系统会自动降级到 IndexedDB 或 localStorage
3. **数据同步**: 目前没有实现 Elasticsearch 和本地存储之间的数据同步机制
4. **人工评估**: 人工评估数据目前仅存储在本地，未同步到 Elasticsearch

## 扩展建议

1. 为人工评估创建专门的 Elasticsearch 索引
2. 实现数据同步机制
3. 添加数据备份和恢复功能
4. 支持批量数据导入导出
