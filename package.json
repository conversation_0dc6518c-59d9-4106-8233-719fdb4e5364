{"name": "repository-designer", "version": "0.1.0", "private": true, "dependencies": {"@dqbd/tiktoken": "^1.0.21", "@elastic/elasticsearch": "^9.0.2", "react": "^18.2.0", "react-dom": "^18.2.0", "react-icons": "^4.12.0", "react-scripts": "5.0.1", "styled-components": "^6.1.1"}, "devDependencies": {"express": "^4.18.2", "http-proxy-middleware": "^2.0.6", "cors": "^2.8.5", "concurrently": "^7.6.0"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject", "es-proxy": "node src/components/elasticsearch-proxy.js", "dev": "concurrently \"npm run es-proxy\" \"npm start\""}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}}