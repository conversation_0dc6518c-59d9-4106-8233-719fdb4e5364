# Repository Design Tool

一个强大的项目结构可视化和AI提示词生成工具，帮助开发者快速设计标准化的项目架构并将其转化为可直接使用的AI提示词。

## 主要功能

- **项目结构可视化**：直观展示项目的目录结构，包括文件夹和文件的组织关系
- **可编辑节点**：支持添加、修改和删除节点，轻松调整项目结构
- **描述管理**：为每个文件和文件夹添加详细的功能描述
- **ASCII树生成**：自动生成清晰的ASCII树形结构表示
- **AI提示词生成**：基于项目结构自动创建高质量的AI提示词
- **AI提示词增强**：使用大模型优化提示词，提升结构清晰度和生成代码质量
- **提示词对比**：支持原始提示词和AI增强提示词的并排对比
- **深色模式**：支持明亮和深色两种主题

## 最新增强功能

### 1. ASCII树形结构优化

- 改进了`generateAsciiTree`函数，使描述显示更加清晰
- 移除了描述周围的括号，采用分隔符和对齐空格优化显示效果
- 为不同长度的文件名添加动态间距，使描述对齐

### 2. AI提示词增强功能

- 实现了完整的提示词增强流程，支持原始和增强版本并排对比
- 添加了流式输出功能，实时显示AI增强过程
- 支持接受/拒绝增强版本的快捷操作

### 3. 错误处理和超时检测

- 增强了流式响应的超时检测和重试机制
- 添加了不完整响应的智能识别和处理功能
- 提升了长文本输出的稳定性和完整性

### 4. 用户界面改进

- 添加了深色模式支持，提供完整的主题切换功能
- 增强了进度指示器，提供更准确的进度反馈
- 添加了提示信息和工具提示，改善用户体验
- 优化了对比视图中的操作流程

### 5. JSON解析增强

- 改进了JSON提取和解析算法，支持多种提取模式
- 增加了部分解析失败时的恢复机制
- 优化了极长JSON响应的处理

### 6. 基于上传文件夹的项目结构优化 🆕

- 新增 `createStructureFromFolderPrompt` 函数，支持基于现有项目文件夹结构生成优化建议
- 提供核心能力驱动的结构分析，识别业务功能与代码组织的匹配度
- 支持详细的变更追踪（保留/移动/重命名/新增/删除）和变更理由说明
- 适用于现有项目重构、代码组织优化和最佳实践应用
- 通过 `promptManager.generateStructureFromFolder()` 方法调用

### 7. 智能 .gitignore 过滤 🆕

- 自动检测并解析项目中的 `.gitignore` 文件
- 支持标准 gitignore 语法（通配符、否定规则、目录匹配等）
- 内置常见忽略规则（node_modules、.git、dist、.DS_Store等）
- 智能过滤上传文件，提供更清洁的项目结构分析
- 显示详细的过滤统计和被忽略文件列表

## 使用方法

### 项目结构生成（三种方式）
1. **基础生成**：基于项目描述从零开始生成标准化结构
2. **GitHub仓库**：输入GitHub仓库URL，基于现有项目结构进行优化
3. **上传文件夹**：上传本地项目文件夹，获得结构分析和优化建议

### 操作流程
1. **创建新项目**：点击顶部的"新建项目"按钮
2. **选择生成方式**：在弹窗中选择基础生成、GitHub仓库或上传文件夹
3. **配置项目信息**：填写项目描述、选择项目类型、提供额外信息
4. **生成项目结构**：点击"智能生成项目结构"获得AI建议
5. **编辑和优化**：使用左侧编辑面板进一步调整结构
6. **生成AI提示词**：点击"生成AI提示词"按钮
7. **增强提示词**：点击魔棒图标启动AI增强
8. **查看对比**：在对比视图中查看原始和增强版本
9. **使用提示词**：复制提示词或直接发送到NoCode平台

## 技术栈

- React.js
- Styled Components
- React Icons
- 大模型API (Claude 3.7 Sonnet)

## 开发说明

1. 克隆仓库
2. 安装依赖: `npm install`
3. 启动开发服务器: `npm start`
4. 构建生产版本: `npm run build`

## 许可证

本项目采用 MIT 许可证。 